# Manual Exposure Control

The CamerAwesome plugin now includes a modern manual exposure control feature that allows users to adjust exposure compensation in real-time for both photo and video capture modes.

## Features

- **Intuitive Slider Control**: Modern slider interface for smooth exposure adjustment
- **Real-time Preview**: Immediate visual feedback as exposure changes
- **EV Display**: Shows current exposure value in standard EV format (-2.0 to +2.0)
- **Auto Reset**: One-tap reset button to return to auto-exposure
- **Cross-platform**: Works on both Android and iOS
- **Mode Compatibility**: Functions in both photo and video capture modes

## Usage

### Basic Implementation

```dart
import 'package:camerawesome/camerawesome_plugin.dart';

// Add the exposure control to your camera UI
AwesomeExposureSelector(
  state: cameraState,
  showResetButton: true,
  sliderActiveColor: Colors.white,
  sliderInactiveColor: Colors.white.withOpacity(0.3),
  textColor: Colors.white,
)
```

### Integration with CameraAwesome Builder

```dart
CameraAwesomeBuilder.awesome(
  // ... other configuration
  middleContentBuilder: (state) {
    return Column(
      children: [
        const Spacer(),
        // Add exposure control
        AwesomeExposureSelector(
          state: state,
          sliderActiveColor: Colors.cyan,
          sliderInactiveColor: Colors.cyan.withOpacity(0.3),
          textColor: Colors.white,
        ),
        const Spacer(),
      ],
    );
  },
)
```

### Custom Styling

```dart
AwesomeExposureSelector(
  state: state,
  showResetButton: true,
  sliderActiveColor: Colors.blue,           // Active slider color
  sliderInactiveColor: Colors.grey,         // Inactive slider color  
  textColor: Colors.white,                  // Text color for EV display
)
```

## Widget Properties

| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `state` | `CameraState` | Required | The camera state from CameraAwesome |
| `showResetButton` | `bool` | `true` | Whether to show the AUTO reset button |
| `sliderActiveColor` | `Color?` | `null` | Color for the active portion of the slider |
| `sliderInactiveColor` | `Color?` | `null` | Color for the inactive portion of the slider |
| `textColor` | `Color?` | `null` | Color for the EV value text display |

## How It Works

The exposure control uses the existing `setBrightness()` method from the CamerAwesome plugin:

1. **Brightness Range**: Maps 0.0-1.0 brightness values to -2.0 to +2.0 EV display
2. **Auto-exposure**: 0.5 brightness value represents auto-exposure (0 EV)
3. **Debouncing**: Changes are debounced to prevent excessive API calls
4. **Platform Implementation**: 
   - **Android**: Uses CameraX exposure compensation
   - **iOS**: Uses AVFoundation exposure target bias

## Examples

### Complete Example

See `example/lib/exposure_control_example.dart` for a comprehensive implementation that includes:

- Photo and video mode support
- Proper UI integration
- Theme customization
- Mode indicators

### Modified Custom UI

See `example/lib/custom_awesome_ui.dart` for an example of integrating exposure control into an existing custom UI.

## Technical Details

### Exposure Value Calculation

```dart
// Convert brightness (0-1) to EV (-2.0 to +2.0)
double brightnessToEV(double brightness) {
  return (brightness - 0.5) * 4.0;
}

// Format EV for display
String formatEV(double ev) {
  if (ev == 0.0) return "0";
  return "${ev > 0 ? '+' : ''}${ev.toStringAsFixed(1)}";
}
```

### Platform-Specific Implementation

The exposure control leverages platform-specific camera APIs:

- **Android (CameraX)**: Uses `setExposureCompensationIndex()` with device-specific compensation ranges
- **iOS (AVFoundation)**: Uses `setExposureTargetBias()` with device min/max exposure bias values

## Best Practices

1. **UI Placement**: Position the control where it's easily accessible but doesn't interfere with capture buttons
2. **Visual Feedback**: Use contrasting colors for good visibility in various lighting conditions
3. **Reset Option**: Always provide a way to return to auto-exposure
4. **Responsive Design**: Consider different screen sizes and orientations
5. **Performance**: The built-in debouncing prevents excessive API calls during slider adjustment

## Troubleshooting

### Common Issues

1. **Control Not Responding**: Ensure the camera state is properly initialized
2. **Visual Glitches**: Check that theme colors have sufficient contrast
3. **Performance Issues**: The debouncing should handle rapid changes automatically

### Debug Tips

```dart
// Monitor brightness changes
widget.state.sensorConfig$.listen((config) {
  print('Sensor config updated');
});
```

## Migration

If you're upgrading from a previous version, simply add the `AwesomeExposureSelector` widget to your existing UI. No breaking changes are introduced.

// Autogenerated from Pig<PERSON> (v21.2.0), do not edit directly.
// See also: https://pub.dev/packages/pigeon

#import "Pigeon.h"

#if TARGET_OS_OSX
#import <FlutterMacOS/FlutterMacOS.h>
#else
#import <Flutter/Flutter.h>
#endif

#if !__has_feature(objc_arc)
#error File requires ARC to be enabled.
#endif

static NSArray<id> *wrapResult(id result, FlutterError *error) {
  if (error) {
    return @[
      error.code ?: [NSNull null], error.message ?: [NSNull null], error.details ?: [NSNull null]
    ];
  }
  return @[ result ?: [NSNull null] ];
}

static id GetNullableObjectAtIndex(NSArray<id> *array, NSInteger key) {
  id result = array[key];
  return (result == [NSNull null]) ? nil : result;
}

@implementation PigeonSensorPositionBox
- (instancetype)initWithValue:(PigeonSensorPosition)value {
  self = [super init];
  if (self) {
    _value = value;
  }
  return self;
}
@end

/// Video recording quality, from [sd] to [uhd], with [highest] and [lowest] to
/// let the device choose the best/worst quality available.
/// [highest] is the default quality.
///
/// Qualities are defined like this:
/// [sd] < [hd] < [fhd] < [uhd]
@implementation VideoRecordingQualityBox
- (instancetype)initWithValue:(VideoRecordingQuality)value {
  self = [super init];
  if (self) {
    _value = value;
  }
  return self;
}
@end

/// If the specified [VideoRecordingQuality] is not available on the device,
/// the [VideoRecordingQuality] will fallback to [higher] or [lower] quality.
/// [higher] is the default fallback strategy.
@implementation QualityFallbackStrategyBox
- (instancetype)initWithValue:(QualityFallbackStrategy)value {
  self = [super init];
  if (self) {
    _value = value;
  }
  return self;
}
@end

@implementation CupertinoFileTypeBox
- (instancetype)initWithValue:(CupertinoFileType)value {
  self = [super init];
  if (self) {
    _value = value;
  }
  return self;
}
@end

@implementation CupertinoCodecTypeBox
- (instancetype)initWithValue:(CupertinoCodecType)value {
  self = [super init];
  if (self) {
    _value = value;
  }
  return self;
}
@end

@implementation PigeonSensorTypeBox
- (instancetype)initWithValue:(PigeonSensorType)value {
  self = [super init];
  if (self) {
    _value = value;
  }
  return self;
}
@end

@implementation CamerAwesomePermissionBox
- (instancetype)initWithValue:(CamerAwesomePermission)value {
  self = [super init];
  if (self) {
    _value = value;
  }
  return self;
}
@end

@implementation AnalysisImageFormatBox
- (instancetype)initWithValue:(AnalysisImageFormat)value {
  self = [super init];
  if (self) {
    _value = value;
  }
  return self;
}
@end

@implementation AnalysisRotationBox
- (instancetype)initWithValue:(AnalysisRotation)value {
  self = [super init];
  if (self) {
    _value = value;
  }
  return self;
}
@end

@interface PreviewSize ()
+ (PreviewSize *)fromList:(NSArray<id> *)list;
+ (nullable PreviewSize *)nullableFromList:(NSArray<id> *)list;
- (NSArray<id> *)toList;
@end

@interface ExifPreferences ()
+ (ExifPreferences *)fromList:(NSArray<id> *)list;
+ (nullable ExifPreferences *)nullableFromList:(NSArray<id> *)list;
- (NSArray<id> *)toList;
@end

@interface PigeonSensor ()
+ (PigeonSensor *)fromList:(NSArray<id> *)list;
+ (nullable PigeonSensor *)nullableFromList:(NSArray<id> *)list;
- (NSArray<id> *)toList;
@end

@interface VideoOptions ()
+ (VideoOptions *)fromList:(NSArray<id> *)list;
+ (nullable VideoOptions *)nullableFromList:(NSArray<id> *)list;
- (NSArray<id> *)toList;
@end

@interface AndroidVideoOptions ()
+ (AndroidVideoOptions *)fromList:(NSArray<id> *)list;
+ (nullable AndroidVideoOptions *)nullableFromList:(NSArray<id> *)list;
- (NSArray<id> *)toList;
@end

@interface CupertinoVideoOptions ()
+ (CupertinoVideoOptions *)fromList:(NSArray<id> *)list;
+ (nullable CupertinoVideoOptions *)nullableFromList:(NSArray<id> *)list;
- (NSArray<id> *)toList;
@end

@interface PigeonSensorTypeDevice ()
+ (PigeonSensorTypeDevice *)fromList:(NSArray<id> *)list;
+ (nullable PigeonSensorTypeDevice *)nullableFromList:(NSArray<id> *)list;
- (NSArray<id> *)toList;
@end

@interface AndroidFocusSettings ()
+ (AndroidFocusSettings *)fromList:(NSArray<id> *)list;
+ (nullable AndroidFocusSettings *)nullableFromList:(NSArray<id> *)list;
- (NSArray<id> *)toList;
@end

@interface PlaneWrapper ()
+ (PlaneWrapper *)fromList:(NSArray<id> *)list;
+ (nullable PlaneWrapper *)nullableFromList:(NSArray<id> *)list;
- (NSArray<id> *)toList;
@end

@interface CropRectWrapper ()
+ (CropRectWrapper *)fromList:(NSArray<id> *)list;
+ (nullable CropRectWrapper *)nullableFromList:(NSArray<id> *)list;
- (NSArray<id> *)toList;
@end

@interface AnalysisImageWrapper ()
+ (AnalysisImageWrapper *)fromList:(NSArray<id> *)list;
+ (nullable AnalysisImageWrapper *)nullableFromList:(NSArray<id> *)list;
- (NSArray<id> *)toList;
@end

@implementation PreviewSize
+ (instancetype)makeWithWidth:(double )width
    height:(double )height {
  PreviewSize* pigeonResult = [[PreviewSize alloc] init];
  pigeonResult.width = width;
  pigeonResult.height = height;
  return pigeonResult;
}
+ (PreviewSize *)fromList:(NSArray<id> *)list {
  PreviewSize *pigeonResult = [[PreviewSize alloc] init];
  pigeonResult.width = [GetNullableObjectAtIndex(list, 0) doubleValue];
  pigeonResult.height = [GetNullableObjectAtIndex(list, 1) doubleValue];
  return pigeonResult;
}
+ (nullable PreviewSize *)nullableFromList:(NSArray<id> *)list {
  return (list) ? [PreviewSize fromList:list] : nil;
}
- (NSArray<id> *)toList {
  return @[
    @(self.width),
    @(self.height),
  ];
}
@end

@implementation ExifPreferences
+ (instancetype)makeWithSaveGPSLocation:(BOOL )saveGPSLocation {
  ExifPreferences* pigeonResult = [[ExifPreferences alloc] init];
  pigeonResult.saveGPSLocation = saveGPSLocation;
  return pigeonResult;
}
+ (ExifPreferences *)fromList:(NSArray<id> *)list {
  ExifPreferences *pigeonResult = [[ExifPreferences alloc] init];
  pigeonResult.saveGPSLocation = [GetNullableObjectAtIndex(list, 0) boolValue];
  return pigeonResult;
}
+ (nullable ExifPreferences *)nullableFromList:(NSArray<id> *)list {
  return (list) ? [ExifPreferences fromList:list] : nil;
}
- (NSArray<id> *)toList {
  return @[
    @(self.saveGPSLocation),
  ];
}
@end

@implementation PigeonSensor
+ (instancetype)makeWithPosition:(PigeonSensorPosition)position
    type:(PigeonSensorType)type
    deviceId:(nullable NSString *)deviceId {
  PigeonSensor* pigeonResult = [[PigeonSensor alloc] init];
  pigeonResult.position = position;
  pigeonResult.type = type;
  pigeonResult.deviceId = deviceId;
  return pigeonResult;
}
+ (PigeonSensor *)fromList:(NSArray<id> *)list {
  PigeonSensor *pigeonResult = [[PigeonSensor alloc] init];
  PigeonSensorPositionBox *boxedPigeonSensorPosition = GetNullableObjectAtIndex(list, 0);
  pigeonResult.position = boxedPigeonSensorPosition.value;
  PigeonSensorTypeBox *boxedPigeonSensorType = GetNullableObjectAtIndex(list, 1);
  pigeonResult.type = boxedPigeonSensorType.value;
  pigeonResult.deviceId = GetNullableObjectAtIndex(list, 2);
  return pigeonResult;
}
+ (nullable PigeonSensor *)nullableFromList:(NSArray<id> *)list {
  return (list) ? [PigeonSensor fromList:list] : nil;
}
- (NSArray<id> *)toList {
  return @[
    [[PigeonSensorPositionBox alloc] initWithValue:self.position],
    [[PigeonSensorTypeBox alloc] initWithValue:self.type],
    self.deviceId ?: [NSNull null],
  ];
}
@end

@implementation VideoOptions
+ (instancetype)makeWithEnableAudio:(BOOL )enableAudio
    quality:(nullable VideoRecordingQualityBox *)quality
    android:(nullable AndroidVideoOptions *)android
    ios:(nullable CupertinoVideoOptions *)ios {
  VideoOptions* pigeonResult = [[VideoOptions alloc] init];
  pigeonResult.enableAudio = enableAudio;
  pigeonResult.quality = quality;
  pigeonResult.android = android;
  pigeonResult.ios = ios;
  return pigeonResult;
}
+ (VideoOptions *)fromList:(NSArray<id> *)list {
  VideoOptions *pigeonResult = [[VideoOptions alloc] init];
  pigeonResult.enableAudio = [GetNullableObjectAtIndex(list, 0) boolValue];
  pigeonResult.quality = GetNullableObjectAtIndex(list, 1);
  pigeonResult.android = GetNullableObjectAtIndex(list, 2);
  pigeonResult.ios = GetNullableObjectAtIndex(list, 3);
  return pigeonResult;
}
+ (nullable VideoOptions *)nullableFromList:(NSArray<id> *)list {
  return (list) ? [VideoOptions fromList:list] : nil;
}
- (NSArray<id> *)toList {
  return @[
    @(self.enableAudio),
    self.quality ?: [NSNull null],
    self.android ?: [NSNull null],
    self.ios ?: [NSNull null],
  ];
}
@end

@implementation AndroidVideoOptions
+ (instancetype)makeWithBitrate:(nullable NSNumber *)bitrate
    fallbackStrategy:(nullable QualityFallbackStrategyBox *)fallbackStrategy {
  AndroidVideoOptions* pigeonResult = [[AndroidVideoOptions alloc] init];
  pigeonResult.bitrate = bitrate;
  pigeonResult.fallbackStrategy = fallbackStrategy;
  return pigeonResult;
}
+ (AndroidVideoOptions *)fromList:(NSArray<id> *)list {
  AndroidVideoOptions *pigeonResult = [[AndroidVideoOptions alloc] init];
  pigeonResult.bitrate = GetNullableObjectAtIndex(list, 0);
  pigeonResult.fallbackStrategy = GetNullableObjectAtIndex(list, 1);
  return pigeonResult;
}
+ (nullable AndroidVideoOptions *)nullableFromList:(NSArray<id> *)list {
  return (list) ? [AndroidVideoOptions fromList:list] : nil;
}
- (NSArray<id> *)toList {
  return @[
    self.bitrate ?: [NSNull null],
    self.fallbackStrategy ?: [NSNull null],
  ];
}
@end

@implementation CupertinoVideoOptions
+ (instancetype)makeWithFileType:(nullable CupertinoFileTypeBox *)fileType
    codec:(nullable CupertinoCodecTypeBox *)codec
    fps:(nullable NSNumber *)fps {
  CupertinoVideoOptions* pigeonResult = [[CupertinoVideoOptions alloc] init];
  pigeonResult.fileType = fileType;
  pigeonResult.codec = codec;
  pigeonResult.fps = fps;
  return pigeonResult;
}
+ (CupertinoVideoOptions *)fromList:(NSArray<id> *)list {
  CupertinoVideoOptions *pigeonResult = [[CupertinoVideoOptions alloc] init];
  pigeonResult.fileType = GetNullableObjectAtIndex(list, 0);
  pigeonResult.codec = GetNullableObjectAtIndex(list, 1);
  pigeonResult.fps = GetNullableObjectAtIndex(list, 2);
  return pigeonResult;
}
+ (nullable CupertinoVideoOptions *)nullableFromList:(NSArray<id> *)list {
  return (list) ? [CupertinoVideoOptions fromList:list] : nil;
}
- (NSArray<id> *)toList {
  return @[
    self.fileType ?: [NSNull null],
    self.codec ?: [NSNull null],
    self.fps ?: [NSNull null],
  ];
}
@end

@implementation PigeonSensorTypeDevice
+ (instancetype)makeWithSensorType:(PigeonSensorType)sensorType
    name:(NSString *)name
    iso:(double )iso
    flashAvailable:(BOOL )flashAvailable
    uid:(NSString *)uid {
  PigeonSensorTypeDevice* pigeonResult = [[PigeonSensorTypeDevice alloc] init];
  pigeonResult.sensorType = sensorType;
  pigeonResult.name = name;
  pigeonResult.iso = iso;
  pigeonResult.flashAvailable = flashAvailable;
  pigeonResult.uid = uid;
  return pigeonResult;
}
+ (PigeonSensorTypeDevice *)fromList:(NSArray<id> *)list {
  PigeonSensorTypeDevice *pigeonResult = [[PigeonSensorTypeDevice alloc] init];
  PigeonSensorTypeBox *boxedPigeonSensorType = GetNullableObjectAtIndex(list, 0);
  pigeonResult.sensorType = boxedPigeonSensorType.value;
  pigeonResult.name = GetNullableObjectAtIndex(list, 1);
  pigeonResult.iso = [GetNullableObjectAtIndex(list, 2) doubleValue];
  pigeonResult.flashAvailable = [GetNullableObjectAtIndex(list, 3) boolValue];
  pigeonResult.uid = GetNullableObjectAtIndex(list, 4);
  return pigeonResult;
}
+ (nullable PigeonSensorTypeDevice *)nullableFromList:(NSArray<id> *)list {
  return (list) ? [PigeonSensorTypeDevice fromList:list] : nil;
}
- (NSArray<id> *)toList {
  return @[
    [[PigeonSensorTypeBox alloc] initWithValue:self.sensorType],
    self.name ?: [NSNull null],
    @(self.iso),
    @(self.flashAvailable),
    self.uid ?: [NSNull null],
  ];
}
@end

@implementation AndroidFocusSettings
+ (instancetype)makeWithAutoCancelDurationInMillis:(NSInteger )autoCancelDurationInMillis {
  AndroidFocusSettings* pigeonResult = [[AndroidFocusSettings alloc] init];
  pigeonResult.autoCancelDurationInMillis = autoCancelDurationInMillis;
  return pigeonResult;
}
+ (AndroidFocusSettings *)fromList:(NSArray<id> *)list {
  AndroidFocusSettings *pigeonResult = [[AndroidFocusSettings alloc] init];
  pigeonResult.autoCancelDurationInMillis = [GetNullableObjectAtIndex(list, 0) integerValue];
  return pigeonResult;
}
+ (nullable AndroidFocusSettings *)nullableFromList:(NSArray<id> *)list {
  return (list) ? [AndroidFocusSettings fromList:list] : nil;
}
- (NSArray<id> *)toList {
  return @[
    @(self.autoCancelDurationInMillis),
  ];
}
@end

@implementation PlaneWrapper
+ (instancetype)makeWithBytes:(FlutterStandardTypedData *)bytes
    bytesPerRow:(NSInteger )bytesPerRow
    bytesPerPixel:(nullable NSNumber *)bytesPerPixel
    width:(nullable NSNumber *)width
    height:(nullable NSNumber *)height {
  PlaneWrapper* pigeonResult = [[PlaneWrapper alloc] init];
  pigeonResult.bytes = bytes;
  pigeonResult.bytesPerRow = bytesPerRow;
  pigeonResult.bytesPerPixel = bytesPerPixel;
  pigeonResult.width = width;
  pigeonResult.height = height;
  return pigeonResult;
}
+ (PlaneWrapper *)fromList:(NSArray<id> *)list {
  PlaneWrapper *pigeonResult = [[PlaneWrapper alloc] init];
  pigeonResult.bytes = GetNullableObjectAtIndex(list, 0);
  pigeonResult.bytesPerRow = [GetNullableObjectAtIndex(list, 1) integerValue];
  pigeonResult.bytesPerPixel = GetNullableObjectAtIndex(list, 2);
  pigeonResult.width = GetNullableObjectAtIndex(list, 3);
  pigeonResult.height = GetNullableObjectAtIndex(list, 4);
  return pigeonResult;
}
+ (nullable PlaneWrapper *)nullableFromList:(NSArray<id> *)list {
  return (list) ? [PlaneWrapper fromList:list] : nil;
}
- (NSArray<id> *)toList {
  return @[
    self.bytes ?: [NSNull null],
    @(self.bytesPerRow),
    self.bytesPerPixel ?: [NSNull null],
    self.width ?: [NSNull null],
    self.height ?: [NSNull null],
  ];
}
@end

@implementation CropRectWrapper
+ (instancetype)makeWithLeft:(NSInteger )left
    top:(NSInteger )top
    width:(NSInteger )width
    height:(NSInteger )height {
  CropRectWrapper* pigeonResult = [[CropRectWrapper alloc] init];
  pigeonResult.left = left;
  pigeonResult.top = top;
  pigeonResult.width = width;
  pigeonResult.height = height;
  return pigeonResult;
}
+ (CropRectWrapper *)fromList:(NSArray<id> *)list {
  CropRectWrapper *pigeonResult = [[CropRectWrapper alloc] init];
  pigeonResult.left = [GetNullableObjectAtIndex(list, 0) integerValue];
  pigeonResult.top = [GetNullableObjectAtIndex(list, 1) integerValue];
  pigeonResult.width = [GetNullableObjectAtIndex(list, 2) integerValue];
  pigeonResult.height = [GetNullableObjectAtIndex(list, 3) integerValue];
  return pigeonResult;
}
+ (nullable CropRectWrapper *)nullableFromList:(NSArray<id> *)list {
  return (list) ? [CropRectWrapper fromList:list] : nil;
}
- (NSArray<id> *)toList {
  return @[
    @(self.left),
    @(self.top),
    @(self.width),
    @(self.height),
  ];
}
@end

@implementation AnalysisImageWrapper
+ (instancetype)makeWithFormat:(AnalysisImageFormat)format
    bytes:(nullable FlutterStandardTypedData *)bytes
    width:(NSInteger )width
    height:(NSInteger )height
    planes:(nullable NSArray<PlaneWrapper *> *)planes
    cropRect:(nullable CropRectWrapper *)cropRect
    rotation:(nullable AnalysisRotationBox *)rotation {
  AnalysisImageWrapper* pigeonResult = [[AnalysisImageWrapper alloc] init];
  pigeonResult.format = format;
  pigeonResult.bytes = bytes;
  pigeonResult.width = width;
  pigeonResult.height = height;
  pigeonResult.planes = planes;
  pigeonResult.cropRect = cropRect;
  pigeonResult.rotation = rotation;
  return pigeonResult;
}
+ (AnalysisImageWrapper *)fromList:(NSArray<id> *)list {
  AnalysisImageWrapper *pigeonResult = [[AnalysisImageWrapper alloc] init];
  AnalysisImageFormatBox *boxedAnalysisImageFormat = GetNullableObjectAtIndex(list, 0);
  pigeonResult.format = boxedAnalysisImageFormat.value;
  pigeonResult.bytes = GetNullableObjectAtIndex(list, 1);
  pigeonResult.width = [GetNullableObjectAtIndex(list, 2) integerValue];
  pigeonResult.height = [GetNullableObjectAtIndex(list, 3) integerValue];
  pigeonResult.planes = GetNullableObjectAtIndex(list, 4);
  pigeonResult.cropRect = GetNullableObjectAtIndex(list, 5);
  pigeonResult.rotation = GetNullableObjectAtIndex(list, 6);
  return pigeonResult;
}
+ (nullable AnalysisImageWrapper *)nullableFromList:(NSArray<id> *)list {
  return (list) ? [AnalysisImageWrapper fromList:list] : nil;
}
- (NSArray<id> *)toList {
  return @[
    [[AnalysisImageFormatBox alloc] initWithValue:self.format],
    self.bytes ?: [NSNull null],
    @(self.width),
    @(self.height),
    self.planes ?: [NSNull null],
    self.cropRect ?: [NSNull null],
    self.rotation ?: [NSNull null],
  ];
}
@end

@interface nullPigeonPigeonCodecReader : FlutterStandardReader
@end
@implementation nullPigeonPigeonCodecReader
- (nullable id)readValueOfType:(UInt8)type {
  switch (type) {
    case 129: {
      NSNumber *enumAsNumber = [self readValue];
      return enumAsNumber == nil ? nil : [[PigeonSensorPositionBox alloc] initWithValue:[enumAsNumber integerValue]];
    }
    case 130: {
      NSNumber *enumAsNumber = [self readValue];
      return enumAsNumber == nil ? nil : [[VideoRecordingQualityBox alloc] initWithValue:[enumAsNumber integerValue]];
    }
    case 131: {
      NSNumber *enumAsNumber = [self readValue];
      return enumAsNumber == nil ? nil : [[QualityFallbackStrategyBox alloc] initWithValue:[enumAsNumber integerValue]];
    }
    case 132: {
      NSNumber *enumAsNumber = [self readValue];
      return enumAsNumber == nil ? nil : [[CupertinoFileTypeBox alloc] initWithValue:[enumAsNumber integerValue]];
    }
    case 133: {
      NSNumber *enumAsNumber = [self readValue];
      return enumAsNumber == nil ? nil : [[CupertinoCodecTypeBox alloc] initWithValue:[enumAsNumber integerValue]];
    }
    case 134: {
      NSNumber *enumAsNumber = [self readValue];
      return enumAsNumber == nil ? nil : [[PigeonSensorTypeBox alloc] initWithValue:[enumAsNumber integerValue]];
    }
    case 135: {
      NSNumber *enumAsNumber = [self readValue];
      return enumAsNumber == nil ? nil : [[CamerAwesomePermissionBox alloc] initWithValue:[enumAsNumber integerValue]];
    }
    case 136: {
      NSNumber *enumAsNumber = [self readValue];
      return enumAsNumber == nil ? nil : [[AnalysisImageFormatBox alloc] initWithValue:[enumAsNumber integerValue]];
    }
    case 137: {
      NSNumber *enumAsNumber = [self readValue];
      return enumAsNumber == nil ? nil : [[AnalysisRotationBox alloc] initWithValue:[enumAsNumber integerValue]];
    }
    case 138: 
      return [PreviewSize fromList:[self readValue]];
    case 139: 
      return [ExifPreferences fromList:[self readValue]];
    case 140: 
      return [PigeonSensor fromList:[self readValue]];
    case 141: 
      return [VideoOptions fromList:[self readValue]];
    case 142: 
      return [AndroidVideoOptions fromList:[self readValue]];
    case 143: 
      return [CupertinoVideoOptions fromList:[self readValue]];
    case 144: 
      return [PigeonSensorTypeDevice fromList:[self readValue]];
    case 145: 
      return [AndroidFocusSettings fromList:[self readValue]];
    case 146: 
      return [PlaneWrapper fromList:[self readValue]];
    case 147: 
      return [CropRectWrapper fromList:[self readValue]];
    case 148: 
      return [AnalysisImageWrapper fromList:[self readValue]];
    default:
      return [super readValueOfType:type];
  }
}
@end

@interface nullPigeonPigeonCodecWriter : FlutterStandardWriter
@end
@implementation nullPigeonPigeonCodecWriter
- (void)writeValue:(id)value {
  if ([value isKindOfClass:[PigeonSensorPositionBox class]]) {
    PigeonSensorPositionBox *box = (PigeonSensorPositionBox *)value;
    [self writeByte:129];
    [self writeValue:(value == nil ? [NSNull null] : [NSNumber numberWithInteger:box.value])];
  } else if ([value isKindOfClass:[VideoRecordingQualityBox class]]) {
    VideoRecordingQualityBox *box = (VideoRecordingQualityBox *)value;
    [self writeByte:130];
    [self writeValue:(value == nil ? [NSNull null] : [NSNumber numberWithInteger:box.value])];
  } else if ([value isKindOfClass:[QualityFallbackStrategyBox class]]) {
    QualityFallbackStrategyBox *box = (QualityFallbackStrategyBox *)value;
    [self writeByte:131];
    [self writeValue:(value == nil ? [NSNull null] : [NSNumber numberWithInteger:box.value])];
  } else if ([value isKindOfClass:[CupertinoFileTypeBox class]]) {
    CupertinoFileTypeBox *box = (CupertinoFileTypeBox *)value;
    [self writeByte:132];
    [self writeValue:(value == nil ? [NSNull null] : [NSNumber numberWithInteger:box.value])];
  } else if ([value isKindOfClass:[CupertinoCodecTypeBox class]]) {
    CupertinoCodecTypeBox *box = (CupertinoCodecTypeBox *)value;
    [self writeByte:133];
    [self writeValue:(value == nil ? [NSNull null] : [NSNumber numberWithInteger:box.value])];
  } else if ([value isKindOfClass:[PigeonSensorTypeBox class]]) {
    PigeonSensorTypeBox *box = (PigeonSensorTypeBox *)value;
    [self writeByte:134];
    [self writeValue:(value == nil ? [NSNull null] : [NSNumber numberWithInteger:box.value])];
  } else if ([value isKindOfClass:[CamerAwesomePermissionBox class]]) {
    CamerAwesomePermissionBox *box = (CamerAwesomePermissionBox *)value;
    [self writeByte:135];
    [self writeValue:(value == nil ? [NSNull null] : [NSNumber numberWithInteger:box.value])];
  } else if ([value isKindOfClass:[AnalysisImageFormatBox class]]) {
    AnalysisImageFormatBox *box = (AnalysisImageFormatBox *)value;
    [self writeByte:136];
    [self writeValue:(value == nil ? [NSNull null] : [NSNumber numberWithInteger:box.value])];
  } else if ([value isKindOfClass:[AnalysisRotationBox class]]) {
    AnalysisRotationBox *box = (AnalysisRotationBox *)value;
    [self writeByte:137];
    [self writeValue:(value == nil ? [NSNull null] : [NSNumber numberWithInteger:box.value])];
  } else if ([value isKindOfClass:[PreviewSize class]]) {
    [self writeByte:138];
    [self writeValue:[value toList]];
  } else if ([value isKindOfClass:[ExifPreferences class]]) {
    [self writeByte:139];
    [self writeValue:[value toList]];
  } else if ([value isKindOfClass:[PigeonSensor class]]) {
    [self writeByte:140];
    [self writeValue:[value toList]];
  } else if ([value isKindOfClass:[VideoOptions class]]) {
    [self writeByte:141];
    [self writeValue:[value toList]];
  } else if ([value isKindOfClass:[AndroidVideoOptions class]]) {
    [self writeByte:142];
    [self writeValue:[value toList]];
  } else if ([value isKindOfClass:[CupertinoVideoOptions class]]) {
    [self writeByte:143];
    [self writeValue:[value toList]];
  } else if ([value isKindOfClass:[PigeonSensorTypeDevice class]]) {
    [self writeByte:144];
    [self writeValue:[value toList]];
  } else if ([value isKindOfClass:[AndroidFocusSettings class]]) {
    [self writeByte:145];
    [self writeValue:[value toList]];
  } else if ([value isKindOfClass:[PlaneWrapper class]]) {
    [self writeByte:146];
    [self writeValue:[value toList]];
  } else if ([value isKindOfClass:[CropRectWrapper class]]) {
    [self writeByte:147];
    [self writeValue:[value toList]];
  } else if ([value isKindOfClass:[AnalysisImageWrapper class]]) {
    [self writeByte:148];
    [self writeValue:[value toList]];
  } else {
    [super writeValue:value];
  }
}
@end

@interface nullPigeonPigeonCodecReaderWriter : FlutterStandardReaderWriter
@end
@implementation nullPigeonPigeonCodecReaderWriter
- (FlutterStandardWriter *)writerWithData:(NSMutableData *)data {
  return [[nullPigeonPigeonCodecWriter alloc] initWithData:data];
}
- (FlutterStandardReader *)readerWithData:(NSData *)data {
  return [[nullPigeonPigeonCodecReader alloc] initWithData:data];
}
@end

NSObject<FlutterMessageCodec> *nullGetPigeonCodec(void) {
  static FlutterStandardMessageCodec *sSharedObject = nil;
  static dispatch_once_t sPred = 0;
  dispatch_once(&sPred, ^{
    nullPigeonPigeonCodecReaderWriter *readerWriter = [[nullPigeonPigeonCodecReaderWriter alloc] init];
    sSharedObject = [FlutterStandardMessageCodec codecWithReaderWriter:readerWriter];
  });
  return sSharedObject;
}
void SetUpAnalysisImageUtils(id<FlutterBinaryMessenger> binaryMessenger, NSObject<AnalysisImageUtils> *api) {
  SetUpAnalysisImageUtilsWithSuffix(binaryMessenger, api, @"");
}

void SetUpAnalysisImageUtilsWithSuffix(id<FlutterBinaryMessenger> binaryMessenger, NSObject<AnalysisImageUtils> *api, NSString *messageChannelSuffix) {
  messageChannelSuffix = messageChannelSuffix.length > 0 ? [NSString stringWithFormat: @".%@", messageChannelSuffix] : @"";
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.AnalysisImageUtils.nv21toJpeg", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(nv21toJpegNv21Image:jpegQuality:completion:)], @"AnalysisImageUtils api (%@) doesn't respond to @selector(nv21toJpegNv21Image:jpegQuality:completion:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray<id> *args = message;
        AnalysisImageWrapper *arg_nv21Image = GetNullableObjectAtIndex(args, 0);
        NSInteger arg_jpegQuality = [GetNullableObjectAtIndex(args, 1) integerValue];
        [api nv21toJpegNv21Image:arg_nv21Image jpegQuality:arg_jpegQuality completion:^(AnalysisImageWrapper *_Nullable output, FlutterError *_Nullable error) {
          callback(wrapResult(output, error));
        }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.AnalysisImageUtils.yuv420toJpeg", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(yuv420toJpegYuvImage:jpegQuality:completion:)], @"AnalysisImageUtils api (%@) doesn't respond to @selector(yuv420toJpegYuvImage:jpegQuality:completion:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray<id> *args = message;
        AnalysisImageWrapper *arg_yuvImage = GetNullableObjectAtIndex(args, 0);
        NSInteger arg_jpegQuality = [GetNullableObjectAtIndex(args, 1) integerValue];
        [api yuv420toJpegYuvImage:arg_yuvImage jpegQuality:arg_jpegQuality completion:^(AnalysisImageWrapper *_Nullable output, FlutterError *_Nullable error) {
          callback(wrapResult(output, error));
        }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.AnalysisImageUtils.yuv420toNv21", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(yuv420toNv21YuvImage:completion:)], @"AnalysisImageUtils api (%@) doesn't respond to @selector(yuv420toNv21YuvImage:completion:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray<id> *args = message;
        AnalysisImageWrapper *arg_yuvImage = GetNullableObjectAtIndex(args, 0);
        [api yuv420toNv21YuvImage:arg_yuvImage completion:^(AnalysisImageWrapper *_Nullable output, FlutterError *_Nullable error) {
          callback(wrapResult(output, error));
        }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.AnalysisImageUtils.bgra8888toJpeg", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(bgra8888toJpegBgra8888image:jpegQuality:completion:)], @"AnalysisImageUtils api (%@) doesn't respond to @selector(bgra8888toJpegBgra8888image:jpegQuality:completion:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray<id> *args = message;
        AnalysisImageWrapper *arg_bgra8888image = GetNullableObjectAtIndex(args, 0);
        NSInteger arg_jpegQuality = [GetNullableObjectAtIndex(args, 1) integerValue];
        [api bgra8888toJpegBgra8888image:arg_bgra8888image jpegQuality:arg_jpegQuality completion:^(AnalysisImageWrapper *_Nullable output, FlutterError *_Nullable error) {
          callback(wrapResult(output, error));
        }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
}
void SetUpCameraInterface(id<FlutterBinaryMessenger> binaryMessenger, NSObject<CameraInterface> *api) {
  SetUpCameraInterfaceWithSuffix(binaryMessenger, api, @"");
}

void SetUpCameraInterfaceWithSuffix(id<FlutterBinaryMessenger> binaryMessenger, NSObject<CameraInterface> *api, NSString *messageChannelSuffix) {
  messageChannelSuffix = messageChannelSuffix.length > 0 ? [NSString stringWithFormat: @".%@", messageChannelSuffix] : @"";
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.CameraInterface.setupCamera", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(setupCameraSensors:aspectRatio:zoom:mirrorFrontCamera:enablePhysicalButton:flashMode:captureMode:enableImageStream:exifPreferences:videoOptions:completion:)], @"CameraInterface api (%@) doesn't respond to @selector(setupCameraSensors:aspectRatio:zoom:mirrorFrontCamera:enablePhysicalButton:flashMode:captureMode:enableImageStream:exifPreferences:videoOptions:completion:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray<id> *args = message;
        NSArray<PigeonSensor *> *arg_sensors = GetNullableObjectAtIndex(args, 0);
        NSString *arg_aspectRatio = GetNullableObjectAtIndex(args, 1);
        double arg_zoom = [GetNullableObjectAtIndex(args, 2) doubleValue];
        BOOL arg_mirrorFrontCamera = [GetNullableObjectAtIndex(args, 3) boolValue];
        BOOL arg_enablePhysicalButton = [GetNullableObjectAtIndex(args, 4) boolValue];
        NSString *arg_flashMode = GetNullableObjectAtIndex(args, 5);
        NSString *arg_captureMode = GetNullableObjectAtIndex(args, 6);
        BOOL arg_enableImageStream = [GetNullableObjectAtIndex(args, 7) boolValue];
        ExifPreferences *arg_exifPreferences = GetNullableObjectAtIndex(args, 8);
        VideoOptions *arg_videoOptions = GetNullableObjectAtIndex(args, 9);
        [api setupCameraSensors:arg_sensors aspectRatio:arg_aspectRatio zoom:arg_zoom mirrorFrontCamera:arg_mirrorFrontCamera enablePhysicalButton:arg_enablePhysicalButton flashMode:arg_flashMode captureMode:arg_captureMode enableImageStream:arg_enableImageStream exifPreferences:arg_exifPreferences videoOptions:arg_videoOptions completion:^(NSNumber *_Nullable output, FlutterError *_Nullable error) {
          callback(wrapResult(output, error));
        }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.CameraInterface.checkPermissions", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(checkPermissionsPermissions:error:)], @"CameraInterface api (%@) doesn't respond to @selector(checkPermissionsPermissions:error:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray<id> *args = message;
        NSArray<NSString *> *arg_permissions = GetNullableObjectAtIndex(args, 0);
        FlutterError *error;
        NSArray<NSString *> *output = [api checkPermissionsPermissions:arg_permissions error:&error];
        callback(wrapResult(output, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  /// Returns given [CamerAwesomePermission] list (as String). Location permission might be
  /// refused but the app should still be able to run.
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.CameraInterface.requestPermissions", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(requestPermissionsSaveGpsLocation:completion:)], @"CameraInterface api (%@) doesn't respond to @selector(requestPermissionsSaveGpsLocation:completion:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray<id> *args = message;
        BOOL arg_saveGpsLocation = [GetNullableObjectAtIndex(args, 0) boolValue];
        [api requestPermissionsSaveGpsLocation:arg_saveGpsLocation completion:^(NSArray<NSString *> *_Nullable output, FlutterError *_Nullable error) {
          callback(wrapResult(output, error));
        }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.CameraInterface.getPreviewTextureId", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(getPreviewTextureIdCameraPosition:error:)], @"CameraInterface api (%@) doesn't respond to @selector(getPreviewTextureIdCameraPosition:error:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray<id> *args = message;
        NSInteger arg_cameraPosition = [GetNullableObjectAtIndex(args, 0) integerValue];
        FlutterError *error;
        NSNumber *output = [api getPreviewTextureIdCameraPosition:arg_cameraPosition error:&error];
        callback(wrapResult(output, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.CameraInterface.takePhoto", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(takePhotoSensors:paths:completion:)], @"CameraInterface api (%@) doesn't respond to @selector(takePhotoSensors:paths:completion:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray<id> *args = message;
        NSArray<PigeonSensor *> *arg_sensors = GetNullableObjectAtIndex(args, 0);
        NSArray<NSString *> *arg_paths = GetNullableObjectAtIndex(args, 1);
        [api takePhotoSensors:arg_sensors paths:arg_paths completion:^(NSNumber *_Nullable output, FlutterError *_Nullable error) {
          callback(wrapResult(output, error));
        }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.CameraInterface.recordVideo", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(recordVideoSensors:paths:completion:)], @"CameraInterface api (%@) doesn't respond to @selector(recordVideoSensors:paths:completion:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray<id> *args = message;
        NSArray<PigeonSensor *> *arg_sensors = GetNullableObjectAtIndex(args, 0);
        NSArray<NSString *> *arg_paths = GetNullableObjectAtIndex(args, 1);
        [api recordVideoSensors:arg_sensors paths:arg_paths completion:^(FlutterError *_Nullable error) {
          callback(wrapResult(nil, error));
        }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.CameraInterface.pauseVideoRecording", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(pauseVideoRecordingWithError:)], @"CameraInterface api (%@) doesn't respond to @selector(pauseVideoRecordingWithError:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        FlutterError *error;
        [api pauseVideoRecordingWithError:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.CameraInterface.resumeVideoRecording", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(resumeVideoRecordingWithError:)], @"CameraInterface api (%@) doesn't respond to @selector(resumeVideoRecordingWithError:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        FlutterError *error;
        [api resumeVideoRecordingWithError:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.CameraInterface.receivedImageFromStream", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(receivedImageFromStreamWithError:)], @"CameraInterface api (%@) doesn't respond to @selector(receivedImageFromStreamWithError:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        FlutterError *error;
        [api receivedImageFromStreamWithError:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.CameraInterface.stopRecordingVideo", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(stopRecordingVideoWithCompletion:)], @"CameraInterface api (%@) doesn't respond to @selector(stopRecordingVideoWithCompletion:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        [api stopRecordingVideoWithCompletion:^(NSNumber *_Nullable output, FlutterError *_Nullable error) {
          callback(wrapResult(output, error));
        }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.CameraInterface.getFrontSensors", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(getFrontSensorsWithError:)], @"CameraInterface api (%@) doesn't respond to @selector(getFrontSensorsWithError:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        FlutterError *error;
        NSArray<PigeonSensorTypeDevice *> *output = [api getFrontSensorsWithError:&error];
        callback(wrapResult(output, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.CameraInterface.getBackSensors", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(getBackSensorsWithError:)], @"CameraInterface api (%@) doesn't respond to @selector(getBackSensorsWithError:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        FlutterError *error;
        NSArray<PigeonSensorTypeDevice *> *output = [api getBackSensorsWithError:&error];
        callback(wrapResult(output, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.CameraInterface.start", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(startWithError:)], @"CameraInterface api (%@) doesn't respond to @selector(startWithError:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        FlutterError *error;
        NSNumber *output = [api startWithError:&error];
        callback(wrapResult(output, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.CameraInterface.stop", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(stopWithError:)], @"CameraInterface api (%@) doesn't respond to @selector(stopWithError:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        FlutterError *error;
        NSNumber *output = [api stopWithError:&error];
        callback(wrapResult(output, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.CameraInterface.setFlashMode", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(setFlashModeMode:error:)], @"CameraInterface api (%@) doesn't respond to @selector(setFlashModeMode:error:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray<id> *args = message;
        NSString *arg_mode = GetNullableObjectAtIndex(args, 0);
        FlutterError *error;
        [api setFlashModeMode:arg_mode error:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.CameraInterface.handleAutoFocus", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(handleAutoFocusWithError:)], @"CameraInterface api (%@) doesn't respond to @selector(handleAutoFocusWithError:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        FlutterError *error;
        [api handleAutoFocusWithError:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  /// Starts auto focus on a point at ([x], [y]).
  ///
  /// On Android, you can control after how much time you want to switch back
  /// to passive focus mode with [androidFocusSettings].
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.CameraInterface.focusOnPoint", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(focusOnPointPreviewSize:x:y:androidFocusSettings:error:)], @"CameraInterface api (%@) doesn't respond to @selector(focusOnPointPreviewSize:x:y:androidFocusSettings:error:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray<id> *args = message;
        PreviewSize *arg_previewSize = GetNullableObjectAtIndex(args, 0);
        double arg_x = [GetNullableObjectAtIndex(args, 1) doubleValue];
        double arg_y = [GetNullableObjectAtIndex(args, 2) doubleValue];
        AndroidFocusSettings *arg_androidFocusSettings = GetNullableObjectAtIndex(args, 3);
        FlutterError *error;
        [api focusOnPointPreviewSize:arg_previewSize x:arg_x y:arg_y androidFocusSettings:arg_androidFocusSettings error:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.CameraInterface.setZoom", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(setZoomZoom:error:)], @"CameraInterface api (%@) doesn't respond to @selector(setZoomZoom:error:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray<id> *args = message;
        double arg_zoom = [GetNullableObjectAtIndex(args, 0) doubleValue];
        FlutterError *error;
        [api setZoomZoom:arg_zoom error:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.CameraInterface.setMirrorFrontCamera", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(setMirrorFrontCameraMirror:error:)], @"CameraInterface api (%@) doesn't respond to @selector(setMirrorFrontCameraMirror:error:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray<id> *args = message;
        BOOL arg_mirror = [GetNullableObjectAtIndex(args, 0) boolValue];
        FlutterError *error;
        [api setMirrorFrontCameraMirror:arg_mirror error:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.CameraInterface.setSensor", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(setSensorSensors:error:)], @"CameraInterface api (%@) doesn't respond to @selector(setSensorSensors:error:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray<id> *args = message;
        NSArray<PigeonSensor *> *arg_sensors = GetNullableObjectAtIndex(args, 0);
        FlutterError *error;
        [api setSensorSensors:arg_sensors error:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.CameraInterface.setCorrection", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(setCorrectionBrightness:error:)], @"CameraInterface api (%@) doesn't respond to @selector(setCorrectionBrightness:error:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray<id> *args = message;
        double arg_brightness = [GetNullableObjectAtIndex(args, 0) doubleValue];
        FlutterError *error;
        [api setCorrectionBrightness:arg_brightness error:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.CameraInterface.setShutterSpeed", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(setShutterSpeedShutterSpeedInSeconds:error:)], @"CameraInterface api (%@) doesn't respond to @selector(setShutterSpeedShutterSpeedInSeconds:error:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray<id> *args = message;
        double arg_shutterSpeedInSeconds = [GetNullableObjectAtIndex(args, 0) doubleValue];
        FlutterError *error;
        [api setShutterSpeedShutterSpeedInSeconds:arg_shutterSpeedInSeconds error:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.CameraInterface.getMinZoom", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(getMinZoomWithError:)], @"CameraInterface api (%@) doesn't respond to @selector(getMinZoomWithError:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        FlutterError *error;
        NSNumber *output = [api getMinZoomWithError:&error];
        callback(wrapResult(output, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.CameraInterface.getMaxZoom", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(getMaxZoomWithError:)], @"CameraInterface api (%@) doesn't respond to @selector(getMaxZoomWithError:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        FlutterError *error;
        NSNumber *output = [api getMaxZoomWithError:&error];
        callback(wrapResult(output, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.CameraInterface.setCaptureMode", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(setCaptureModeMode:error:)], @"CameraInterface api (%@) doesn't respond to @selector(setCaptureModeMode:error:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray<id> *args = message;
        NSString *arg_mode = GetNullableObjectAtIndex(args, 0);
        FlutterError *error;
        [api setCaptureModeMode:arg_mode error:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.CameraInterface.setRecordingAudioMode", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(setRecordingAudioModeEnableAudio:completion:)], @"CameraInterface api (%@) doesn't respond to @selector(setRecordingAudioModeEnableAudio:completion:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray<id> *args = message;
        BOOL arg_enableAudio = [GetNullableObjectAtIndex(args, 0) boolValue];
        [api setRecordingAudioModeEnableAudio:arg_enableAudio completion:^(NSNumber *_Nullable output, FlutterError *_Nullable error) {
          callback(wrapResult(output, error));
        }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.CameraInterface.availableSizes", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(availableSizesWithError:)], @"CameraInterface api (%@) doesn't respond to @selector(availableSizesWithError:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        FlutterError *error;
        NSArray<PreviewSize *> *output = [api availableSizesWithError:&error];
        callback(wrapResult(output, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.CameraInterface.refresh", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(refreshWithError:)], @"CameraInterface api (%@) doesn't respond to @selector(refreshWithError:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        FlutterError *error;
        [api refreshWithError:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.CameraInterface.getEffectivPreviewSize", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(getEffectivPreviewSizeIndex:error:)], @"CameraInterface api (%@) doesn't respond to @selector(getEffectivPreviewSizeIndex:error:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray<id> *args = message;
        NSInteger arg_index = [GetNullableObjectAtIndex(args, 0) integerValue];
        FlutterError *error;
        PreviewSize *output = [api getEffectivPreviewSizeIndex:arg_index error:&error];
        callback(wrapResult(output, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.CameraInterface.setPhotoSize", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(setPhotoSizeSize:error:)], @"CameraInterface api (%@) doesn't respond to @selector(setPhotoSizeSize:error:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray<id> *args = message;
        PreviewSize *arg_size = GetNullableObjectAtIndex(args, 0);
        FlutterError *error;
        [api setPhotoSizeSize:arg_size error:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.CameraInterface.setPreviewSize", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(setPreviewSizeSize:error:)], @"CameraInterface api (%@) doesn't respond to @selector(setPreviewSizeSize:error:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray<id> *args = message;
        PreviewSize *arg_size = GetNullableObjectAtIndex(args, 0);
        FlutterError *error;
        [api setPreviewSizeSize:arg_size error:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.CameraInterface.setAspectRatio", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(setAspectRatioAspectRatio:error:)], @"CameraInterface api (%@) doesn't respond to @selector(setAspectRatioAspectRatio:error:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray<id> *args = message;
        NSString *arg_aspectRatio = GetNullableObjectAtIndex(args, 0);
        FlutterError *error;
        [api setAspectRatioAspectRatio:arg_aspectRatio error:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.CameraInterface.setupImageAnalysisStream", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(setupImageAnalysisStreamFormat:width:maxFramesPerSecond:autoStart:error:)], @"CameraInterface api (%@) doesn't respond to @selector(setupImageAnalysisStreamFormat:width:maxFramesPerSecond:autoStart:error:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray<id> *args = message;
        NSString *arg_format = GetNullableObjectAtIndex(args, 0);
        NSInteger arg_width = [GetNullableObjectAtIndex(args, 1) integerValue];
        NSNumber *arg_maxFramesPerSecond = GetNullableObjectAtIndex(args, 2);
        BOOL arg_autoStart = [GetNullableObjectAtIndex(args, 3) boolValue];
        FlutterError *error;
        [api setupImageAnalysisStreamFormat:arg_format width:arg_width maxFramesPerSecond:arg_maxFramesPerSecond autoStart:arg_autoStart error:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.CameraInterface.setExifPreferences", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(setExifPreferencesExifPreferences:completion:)], @"CameraInterface api (%@) doesn't respond to @selector(setExifPreferencesExifPreferences:completion:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray<id> *args = message;
        ExifPreferences *arg_exifPreferences = GetNullableObjectAtIndex(args, 0);
        [api setExifPreferencesExifPreferences:arg_exifPreferences completion:^(NSNumber *_Nullable output, FlutterError *_Nullable error) {
          callback(wrapResult(output, error));
        }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.CameraInterface.startAnalysis", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(startAnalysisWithError:)], @"CameraInterface api (%@) doesn't respond to @selector(startAnalysisWithError:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        FlutterError *error;
        [api startAnalysisWithError:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.CameraInterface.stopAnalysis", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(stopAnalysisWithError:)], @"CameraInterface api (%@) doesn't respond to @selector(stopAnalysisWithError:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        FlutterError *error;
        [api stopAnalysisWithError:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.CameraInterface.setFilter", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(setFilterMatrix:error:)], @"CameraInterface api (%@) doesn't respond to @selector(setFilterMatrix:error:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray<id> *args = message;
        NSArray<double> *arg_matrix = GetNullableObjectAtIndex(args, 0);
        FlutterError *error;
        [api setFilterMatrix:arg_matrix error:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.CameraInterface.isVideoRecordingAndImageAnalysisSupported", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(isVideoRecordingAndImageAnalysisSupportedSensor:completion:)], @"CameraInterface api (%@) doesn't respond to @selector(isVideoRecordingAndImageAnalysisSupportedSensor:completion:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray<id> *args = message;
        PigeonSensorPositionBox *boxedPigeonSensorPosition = GetNullableObjectAtIndex(args, 0);
        PigeonSensorPosition arg_sensor = boxedPigeonSensorPosition.value;
        [api isVideoRecordingAndImageAnalysisSupportedSensor:arg_sensor completion:^(NSNumber *_Nullable output, FlutterError *_Nullable error) {
          callback(wrapResult(output, error));
        }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:[NSString stringWithFormat:@"%@%@", @"dev.flutter.pigeon.camerawesome.CameraInterface.isMultiCamSupported", messageChannelSuffix]
        binaryMessenger:binaryMessenger
        codec:nullGetPigeonCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(isMultiCamSupportedWithError:)], @"CameraInterface api (%@) doesn't respond to @selector(isMultiCamSupportedWithError:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        FlutterError *error;
        NSNumber *output = [api isMultiCamSupportedWithError:&error];
        callback(wrapResult(output, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
}

# Contributing to CamerAwesome

## Reporting issues
You can easily report issues using GitHub.

Please include maximum information like:
- 🎯 Summary of the issue.
- 🚶‍♂️ Steps to reproduce.
- 😃 What you expected would happen.
- 🤔 What actually happens.
- 📝 Notes (possibly including why you think this might be happening, or stuff you tried that didn't work).

## Creating a Pull Request

### Code reviews
All submissions, including submissions by project members, require review.

### Match the actual coding style
Please use the actual project coding style.

- 2 spaces for indentation.
- Use Line Feed **LF**.
- Run ```flutter analyze``` before submit.

## License
By contributing, you agree that your contributions will be licensed under [CamerAwesome's licence](https://github.com/Apparence-io/camera_awesome/blob/master/LICENSE).
import 'dart:io';
import 'package:camerawesome/camerawesome_plugin.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';

void main() {
  runApp(const CameraAwesomeApp());
}

class CameraAwesomeApp extends StatelessWidget {
  const CameraAwesomeApp({super.key});

  @override
  Widget build(BuildContext context) {
    return const MaterialApp(
      title: 'CamerAwesome Shutter Speed Control',
      home: CameraPage(),
    );
  }
}

class CameraPage extends StatelessWidget {
  const CameraPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CameraAwesomeBuilder.awesome(
        saveConfig: SaveConfig.photoAndVideo(
          initialCaptureMode: CaptureMode.photo,
          photoPathBuilder: (sensors) async {
            final Directory extDir = await getTemporaryDirectory();
            final testDir = await Directory(
              '${extDir.path}/camerawesome',
            ).create(recursive: true);
            if (sensors.length == 1) {
              final String filePath =
                  '${testDir.path}/${DateTime.now().millisecondsSinceEpoch}.jpg';
              return SingleCaptureRequest(filePath, sensors.first);
            }
            return MultipleCaptureRequest(
              {
                for (final sensor in sensors)
                  sensor:
                      '${testDir.path}/${sensor.position == SensorPosition.front ? 'front_' : "back_"}${DateTime.now().millisecondsSinceEpoch}.jpg',
              },
            );
          },
          videoPathBuilder: (sensors) async {
            final Directory extDir = await getTemporaryDirectory();
            final testDir = await Directory(
              '${extDir.path}/camerawesome',
            ).create(recursive: true);
            if (sensors.length == 1) {
              final String filePath =
                  '${testDir.path}/${DateTime.now().millisecondsSinceEpoch}.mp4';
              return SingleCaptureRequest(filePath, sensors.first);
            }
            return MultipleCaptureRequest(
              {
                for (final sensor in sensors)
                  sensor:
                      '${testDir.path}/${sensor.position == SensorPosition.front ? 'front_' : "back_"}${DateTime.now().millisecondsSinceEpoch}.mp4',
              },
            );
          },
        ),
        sensorConfig: SensorConfig.single(
          sensor: Sensor.position(SensorPosition.back),
          flashMode: FlashMode.auto,
          aspectRatio: CameraAspectRatios.ratio_4_3,
          zoom: 0.0,
        ),
        previewFit: CameraPreviewFit.fitWidth,
        onMediaTap: (mediaCapture) {
          // Handle media tap - could open gallery or preview
          debugPrint('Media captured: ${mediaCapture.captureRequest}');
        },
        topActionsBuilder: (state) => AwesomeTopActions(
          state: state,
          children: [
            AwesomeFlashButton(state: state),
            const Spacer(),
            AwesomeCameraSwitchButton(
              state: state,
              scale: 1.0,
              onSwitchTap: (state) {
                state.switchCameraSensor(
                  aspectRatio: state.sensorConfig.aspectRatio,
                );
              },
            ),
          ],
        ),
        middleContentBuilder: (state) {
          return Column(
            children: [
              const Spacer(),
              // Shutter speed control positioned in the middle-right area
              Align(
                alignment: Alignment.centerRight,
                child: Padding(
                  padding: const EdgeInsets.only(right: 20),
                  child: AwesomeShutterSpeedSelector(
                    state: state,
                    sliderActiveColor: Colors.white,
                    sliderInactiveColor: Colors.white.withOpacity(0.3),
                    textColor: Colors.white,
                  ),
                ),
              ),
              const Spacer(),
            ],
          );
        },
        bottomActionsBuilder: (state) => AwesomeBottomActions(
          state: state,
          left: AwesomeFlashButton(state: state),
          captureButton: AwesomeCaptureButton(
            state: state,
          ),
          right: AwesomeCameraSwitchButton(
            state: state,
            scale: 1.0,
            onSwitchTap: (state) {
              state.switchCameraSensor(
                aspectRatio: state.sensorConfig.aspectRatio,
              );
            },
          ),
        ),
      ),
    );
  }
}

/// Example showing shutter speed control with exposure control
class ShutterSpeedWithExposureExample extends StatelessWidget {
  const ShutterSpeedWithExposureExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CameraAwesomeBuilder.awesome(
        saveConfig: SaveConfig.photoAndVideo(
          initialCaptureMode: CaptureMode.photo,
        ),
        sensorConfig: SensorConfig.single(
          sensor: Sensor.position(SensorPosition.back),
          flashMode: FlashMode.auto,
          aspectRatio: CameraAspectRatios.ratio_16_9,
        ),
        previewFit: CameraPreviewFit.fitWidth,
        middleContentBuilder: (state) {
          return Column(
            children: [
              const Spacer(),
              // Both shutter speed and exposure controls
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  // Exposure control on the left
                  AwesomeExposureSelector(
                    state: state,
                    sliderActiveColor: Colors.cyan,
                    sliderInactiveColor: Colors.cyan.withOpacity(0.3),
                    textColor: Colors.white,
                  ),
                  // Shutter speed control on the right
                  AwesomeShutterSpeedSelector(
                    state: state,
                    sliderActiveColor: Colors.orange,
                    sliderInactiveColor: Colors.orange.withOpacity(0.3),
                    textColor: Colors.white,
                  ),
                ],
              ),
              const Spacer(),
            ],
          );
        },
        topActionsBuilder: (state) => AwesomeTopActions(
          state: state,
          children: [
            // Info banner about preview effects
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.black54,
                borderRadius: BorderRadius.circular(20),
              ),
              child: StreamBuilder<double>(
                stream: state.shutterSpeed$,
                builder: (context, snapshot) {
                  final shutterSpeed = snapshot.data ?? -1.0;
                  String effectText = "Auto Mode";

                  if (shutterSpeed > 0) {
                    if (shutterSpeed >= 1.0) {
                      effectText = "Long Exposure: Motion Blur + Light Trails";
                    } else if (shutterSpeed >= 0.125) {
                      effectText = "Slow Shutter: Motion Blur Visible";
                    } else if (shutterSpeed <= 0.001) {
                      effectText = "Fast Shutter: Darker, Crisp Motion";
                    } else {
                      effectText = "Medium Shutter: Balanced Exposure";
                    }
                  }

                  return Text(
                    effectText,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  );
                },
              ),
            ),
          ],
        ),
        bottomActionsBuilder: (state) => AwesomeBottomActions(
          state: state,
          captureButton: AwesomeCaptureButton(state: state),
        ),
      ),
    );
  }
}

/// Example showing compact shutter speed control
class CompactShutterSpeedExample extends StatelessWidget {
  const CompactShutterSpeedExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CameraAwesomeBuilder.awesome(
        saveConfig: SaveConfig.photo(),
        sensorConfig: SensorConfig.single(
          sensor: Sensor.position(SensorPosition.back),
          aspectRatio: CameraAspectRatios.ratio_1_1,
        ),
        bottomActionsBuilder: (state) => Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Compact shutter speed control at the bottom
            AwesomeCompactShutterSpeedSelector(
              state: state,
              sliderActiveColor: Colors.white,
              sliderInactiveColor: Colors.white.withOpacity(0.3),
            ),
            const SizedBox(height: 8),
            AwesomeBottomActions(
              state: state,
              captureButton: AwesomeCaptureButton(state: state),
            ),
          ],
        ),
      ),
    );
  }
}

/// Example showing shutter speed control with custom performance config
class PerformanceOptimizedShutterSpeedExample extends StatelessWidget {
  const PerformanceOptimizedShutterSpeedExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CameraAwesomeBuilder.awesome(
        saveConfig: SaveConfig.photo(),
        sensorConfig: SensorConfig.single(
          sensor: Sensor.position(SensorPosition.back),
          aspectRatio: CameraAspectRatios.ratio_4_3,
        ),
        middleContentBuilder: (state) {
          return Column(
            children: [
              const Spacer(),
              Align(
                alignment: Alignment.centerRight,
                child: Padding(
                  padding: const EdgeInsets.only(right: 20),
                  child: AwesomeShutterSpeedSelector(
                    state: state,
                    sliderActiveColor: Colors.green,
                    sliderInactiveColor: Colors.green.withOpacity(0.3),
                    textColor: Colors.white,
                    performanceConfig: ShutterSpeedPerformanceConfig.highPerformance(),
                  ),
                ),
              ),
              const Spacer(),
            ],
          );
        },
        bottomActionsBuilder: (state) => AwesomeBottomActions(
          state: state,
          captureButton: AwesomeCaptureButton(state: state),
        ),
      ),
    );
  }
}

/// Example demonstrating shutter speed preview effects
class ShutterSpeedPreviewEffectsExample extends StatelessWidget {
  const ShutterSpeedPreviewEffectsExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Shutter Speed Preview Effects'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
      ),
      body: CameraAwesomeBuilder.awesome(
        saveConfig: SaveConfig.photo(),
        sensorConfig: SensorConfig.single(
          sensor: Sensor.position(SensorPosition.back),
          aspectRatio: CameraAspectRatios.ratio_16_9,
        ),
        previewFit: CameraPreviewFit.fitWidth,
        topActionsBuilder: (state) => AwesomeTopActions(
          state: state,
          children: [
            // Real-time effect indicator
            StreamBuilder<double>(
              stream: state.shutterSpeed$,
              builder: (context, snapshot) {
                final shutterSpeed = snapshot.data ?? -1.0;
                return _buildEffectIndicator(shutterSpeed);
              },
            ),
          ],
        ),
        middleContentBuilder: (state) {
          return Column(
            children: [
              const Spacer(),
              // Instructions panel
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 20),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.black54,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'Real-time Preview Effects:',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      '• Fast speeds (1/1000s): Darker, crisp preview\n'
                      '• Slow speeds (1/8s+): Motion blur simulation\n'
                      '• Very slow (1s+): Light trails & frame blending',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),
              // Shutter speed control
              AwesomeShutterSpeedSelector(
                state: state,
                sliderActiveColor: Colors.orange,
                sliderInactiveColor: Colors.orange.withOpacity(0.3),
                textColor: Colors.white,
                showLabel: true,
              ),
              const Spacer(),
            ],
          );
        },
        bottomActionsBuilder: (state) => AwesomeBottomActions(
          state: state,
          captureButton: AwesomeCaptureButton(state: state),
        ),
      ),
    );
  }

  Widget _buildEffectIndicator(double shutterSpeed) {
    String effectName;
    Color effectColor;
    IconData effectIcon;

    if (shutterSpeed < 0) {
      effectName = "AUTO";
      effectColor = Colors.grey;
      effectIcon = Icons.auto_mode;
    } else if (shutterSpeed >= 2.0) {
      effectName = "LONG EXPOSURE";
      effectColor = Colors.purple;
      effectIcon = Icons.blur_on;
    } else if (shutterSpeed >= 0.5) {
      effectName = "MOTION BLUR";
      effectColor = Colors.blue;
      effectIcon = Icons.motion_photos_on;
    } else if (shutterSpeed >= 0.125) {
      effectName = "SLOW SHUTTER";
      effectColor = Colors.orange;
      effectIcon = Icons.slow_motion_video;
    } else if (shutterSpeed <= 0.001) {
      effectName = "FREEZE MOTION";
      effectColor = Colors.red;
      effectIcon = Icons.camera_alt;
    } else {
      effectName = "BALANCED";
      effectColor = Colors.green;
      effectIcon = Icons.balance;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: effectColor.withOpacity(0.8),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(effectIcon, color: Colors.white, size: 16),
          const SizedBox(width: 6),
          Text(
            effectName,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}

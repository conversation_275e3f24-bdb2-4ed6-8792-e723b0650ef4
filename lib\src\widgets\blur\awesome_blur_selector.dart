import 'dart:async';
import 'package:camerawesome/src/orchestrator/states/states.dart';
import 'package:camerawesome/src/widgets/utils/awesome_theme.dart';
import 'package:camerawesome/src/widgets/blur/blur_performance_monitor.dart';
import 'package:flutter/material.dart';

/// A horizontal slider widget for adjusting blur intensity
/// Positioned at the bottom of the camera interface
class AwesomeBlurSelector extends StatefulWidget {
  final CameraState state;
  final Color? sliderActiveColor;
  final Color? sliderInactiveColor;
  final Color? textColor;
  final EdgeInsets padding;
  final bool showLabel;
  final Duration debounceDuration;
  final BlurPerformanceConfig? performanceConfig;

  const AwesomeBlurSelector({
    super.key,
    required this.state,
    this.sliderActiveColor,
    this.sliderInactiveColor,
    this.textColor,
    this.padding = const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
    this.showLabel = true,
    this.debounceDuration = const Duration(milliseconds: 100),
    this.performanceConfig,
  });

  @override
  State<AwesomeBlurSelector> createState() => _AwesomeBlurSelectorState();
}

class _AwesomeBlurSelectorState extends State<AwesomeBlurSelector> {
  double _currentBlur = 0.0;
  Timer? _debounceTimer;
  late BlurPerformanceConfig _config;
  bool _isDragging = false; // New flag to track if slider is being dragged

  @override
  void initState() {
    super.initState();
    _currentBlur = widget.state.blurIntensity;
    _config = widget.performanceConfig ?? BlurPerformanceConfig.auto();
  }

  void _onBlurChanged(double value) {
    setState(() {
      _currentBlur = value;
    });

    // Debounce the actual state update to prevent excessive processing
    _debounceTimer?.cancel();
    _debounceTimer = Timer(_config.debounceDuration, () {
      if (mounted) {
        widget.state.setBlurIntensity(value);
      }
    });
  }

  void _onBlurChangeStart(double value) {
    setState(() {
      _isDragging = true;
    });
  }

  void _onBlurChangeEnd(double value) {
    setState(() {
      _isDragging = false;
    });
    // Ensure the value is set after dragging ends
    widget.state.setBlurIntensity(value);
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = AwesomeThemeProvider.of(context).theme;
    
    return StreamBuilder<double>(
      stream: widget.state.blur$,
      builder: (context, snapshot) {
        // Update local state when stream changes (e.g., from external source)
        // Update local state when stream changes (e.g., from external source)
        // Only update if not currently dragging the slider
        if (snapshot.hasData && snapshot.data != _currentBlur && !_isDragging) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              setState(() {
                _currentBlur = snapshot.data!;
              });
            }
          });
        }

        return Container(
          padding: widget.padding,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (widget.showLabel)
                Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Text(
                    'Blur: ${_currentBlur.toStringAsFixed(1)}',
                    style: TextStyle(
                      color: widget.textColor ?? Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      shadows: const [
                        Shadow(
                          blurRadius: 2,
                          color: Colors.black54,
                        ),
                      ],
                    ),
                  ),
                ),
              Row(
                children: [
                  // Blur off indicator
                  Icon(
                    Icons.blur_off,
                    color: (widget.textColor ?? Colors.white).withOpacity(0.7),
                    size: 16,
                  ),
                  
                  const SizedBox(width: 12),
                  
                  // Slider
                  Expanded(
                    child: SliderTheme(
                      data: SliderTheme.of(context).copyWith(
                        activeTrackColor: widget.sliderActiveColor ?? 
                            theme.buttonTheme.foregroundColor,
                        inactiveTrackColor: widget.sliderInactiveColor ?? 
                            Colors.white30,
                        thumbColor: widget.sliderActiveColor ?? 
                            theme.buttonTheme.foregroundColor,
                        overlayColor: (widget.sliderActiveColor ?? 
                            theme.buttonTheme.foregroundColor).withOpacity(0.2),
                        trackHeight: 3,
                        thumbShape: const RoundSliderThumbShape(
                          enabledThumbRadius: 10,
                        ),
                        overlayShape: const RoundSliderOverlayShape(
                          overlayRadius: 16,
                        ),
                      ),
                      child: Slider(
                        value: _currentBlur,
                        min: 0.0,
                        max: _config.maxBlurIntensity,
                        divisions: (_config.maxBlurIntensity * 4).round(), // 0.25 increments
                        onChanged: _onBlurChanged,
                        onChangeStart: _onBlurChangeStart,
                        onChangeEnd: _onBlurChangeEnd,
                      ),
                    ),
                  ),
                  
                  const SizedBox(width: 12),
                  
                  // Blur on indicator
                  Icon(
                    Icons.blur_on,
                    color: (widget.textColor ?? Colors.white).withOpacity(0.7),
                    size: 16,
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}

/// A compact version of the blur selector without label
class AwesomeCompactBlurSelector extends StatelessWidget {
  final CameraState state;
  final Color? sliderActiveColor;
  final Color? sliderInactiveColor;
  final EdgeInsets padding;

  const AwesomeCompactBlurSelector({
    super.key,
    required this.state,
    this.sliderActiveColor,
    this.sliderInactiveColor,
    this.padding = const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
  });

  @override
  Widget build(BuildContext context) {
    return AwesomeBlurSelector(
      state: state,
      sliderActiveColor: sliderActiveColor,
      sliderInactiveColor: sliderInactiveColor,
      padding: padding,
      showLabel: false,
    );
  }
}

/// A toggle button version for blur on/off with preset intensity
class AwesomeBlurToggleButton extends StatefulWidget {
  final CameraState state;
  final double presetIntensity;
  final Widget? icon;
  const AwesomeBlurToggleButton({
    super.key,
    required this.state,
    this.presetIntensity = 5.0,
    this.icon,
  });

  @override
  State<AwesomeBlurToggleButton> createState() => _AwesomeBlurToggleButtonState();
}

class _AwesomeBlurToggleButtonState extends State<AwesomeBlurToggleButton> {
  bool _isBlurActive = false;

  @override
  void initState() {
    super.initState();
    _isBlurActive = widget.state.blurIntensity > 0.0;
  }

  void _toggleBlur() {
    final newIntensity = _isBlurActive ? 0.0 : widget.presetIntensity;
    widget.state.setBlurIntensity(newIntensity);
    setState(() {
      _isBlurActive = !_isBlurActive;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = AwesomeThemeProvider.of(context).theme;
    
    return StreamBuilder<double>(
      stream: widget.state.blur$,
      builder: (context, snapshot) {
        final isActive = (snapshot.data ?? 0.0) > 0.0;
        
        return ClipOval(
          child: Material(
            color: Colors.transparent, // Always transparent like top buttons
            child: InkWell(
              splashColor: Colors.white.withOpacity(0.3),
              highlightColor: Colors.white.withOpacity(0.1),
              onTap: _toggleBlur,
              child: Padding(
                padding: theme.buttonTheme.padding,
                child: widget.icon ?? Icon(
                  Icons.landscape, // Changed from blur_on/blur_off to landscape
                  color: isActive ? theme.buttonTheme.foregroundColor : theme.buttonTheme.foregroundColor.withOpacity(0.5), // Adjust color based on isActive
                  size: theme.buttonTheme.iconSize,
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

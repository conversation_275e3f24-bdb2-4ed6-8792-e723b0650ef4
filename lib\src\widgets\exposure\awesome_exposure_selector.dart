import 'package:camerawesome/camerawesome_plugin.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AwesomeExposureSelector extends StatefulWidget {
  final CameraState state;
  final bool showResetButton;
  final Color? sliderActiveColor;
  final Color? sliderInactiveColor;
  final Color? textColor;
  final ValueNotifier<bool>? visibilityNotifier; // Added
  final bool showButton; // Added

  const AwesomeExposureSelector({
    super.key,
    required this.state,
    this.showResetButton = true,
    this.sliderActiveColor,
    this.sliderInactiveColor,
    this.textColor,
    this.visibilityNotifier, // Added
    this.showButton = true, // Added
  });

  @override
  State<AwesomeExposureSelector> createState() => _AwesomeExposureSelectorState();
}

class _AwesomeExposureSelectorState extends State<AwesomeExposureSelector> {
  double _currentBrightness = 0.5; // Default to auto-exposure (middle value)
  
  // Use a local ValueNotifier if none is provided externally
  late final ValueNotifier<bool> _internalVisibilityNotifier;

  @override
  void initState() {
    super.initState();
    // Initialize with current brightness if available
    _initializeBrightness();
    _internalVisibilityNotifier = widget.visibilityNotifier ?? ValueNotifier(false);
  }

  @override
  void dispose() {
    // Only dispose the internal notifier if it was created by this widget
    if (widget.visibilityNotifier == null) {
      _internalVisibilityNotifier.dispose();
    }
    super.dispose();
  }

  void _initializeBrightness() {
    // Try to get current brightness from sensor config if available
    widget.state.sensorConfig$.listen((sensorConfig) {
      // Since brightness is not directly exposed in the stream,
      // we'll start with the default middle value
      if (mounted) {
        setState(() {
          _currentBrightness = 0.5;
        });
      }
    });
  }

  void _onBrightnessChanged(double value) {
    setState(() {
      _currentBrightness = value;
    });

    // Apply brightness change immediately for real-time preview
    CamerawesomePlugin.setBrightness(value);

    // Also call the debounced version to maintain architecture consistency
    widget.state.sensorConfig.setBrightness(value);

    // Provide haptic feedback for better UX
    HapticFeedback.selectionClick();
  }

  void _resetExposure() {
    setState(() {
      _currentBrightness = 0.5;
    });

    // Apply reset immediately for real-time preview
    CamerawesomePlugin.setBrightness(0.5);

    // Also call the debounced version to maintain architecture consistency
    widget.state.sensorConfig.setBrightness(0.5);

    HapticFeedback.mediumImpact();
  }

  void _toggleVisibility() {
    _internalVisibilityNotifier.value = !_internalVisibilityNotifier.value;
    HapticFeedback.lightImpact();
  }

  // Convert brightness value (0-1) to EV display (-2.0 to ****)
  double _brightnessToEV(double brightness) {
    return (brightness - 0.5) * 4.0; // Maps 0-1 to -2.0 to ****
  }

  // Format EV value for display
  String _formatEV(double ev) {
    if (ev == 0.0) return "0";
    return "${ev > 0 ? '+' : ''}${ev.toStringAsFixed(1)}";
  }

  @override
  Widget build(BuildContext context) {
    final theme = AwesomeThemeProvider.of(context).theme;
    final buttonTheme = theme.buttonTheme;
    
    return StreamBuilder<SensorConfig>(
      stream: widget.state.sensorConfig$,
      builder: (context, sensorConfigSnapshot) {
        if (!sensorConfigSnapshot.hasData) {
          return const SizedBox.shrink();
        }

        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Exposure toggle button (conditionally rendered)
            if (widget.showButton)
              AwesomeOrientedWidget(
                rotateWithDevice: buttonTheme.rotateWithCamera,
                child: buttonTheme.buttonBuilder(
                  AwesomeCircleWidget.icon(
                    icon: Icons.exposure,
                    theme: theme,
                  ),
                  _toggleVisibility,
                ),
              ),
            
            // Exposure control panel
            ValueListenableBuilder<bool>(
              valueListenable: _internalVisibilityNotifier,
              builder: (context, isVisible, child) {
                return AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeInOut,
                  height: isVisible ? 120 : 0,
                  child: isVisible ? _buildExposurePanel(theme) : null,
                );
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildExposurePanel(AwesomeTheme theme) {
    final ev = _brightnessToEV(_currentBrightness);
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      margin: const EdgeInsets.only(top: 8),
      decoration: BoxDecoration(
        color: theme.bottomActionsBackgroundColor,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // EV display
          Text(
            "${_formatEV(ev)} EV",
            style: TextStyle(
              color: widget.textColor ?? Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Exposure slider
          Row(
            children: [
              // Under-exposure indicator
              Icon(
                Icons.brightness_low,
                color: widget.textColor ?? Colors.white70,
                size: 16,
              ),
              
              const SizedBox(width: 8),
              
              // Slider
              Expanded(
                child: SliderTheme(
                  data: SliderTheme.of(context).copyWith(
                    activeTrackColor: widget.sliderActiveColor ?? Colors.white,
                    inactiveTrackColor: widget.sliderInactiveColor ?? Colors.white30,
                    thumbColor: widget.sliderActiveColor ?? Colors.white,
                    overlayColor: (widget.sliderActiveColor ?? Colors.white).withOpacity(0.2),
                    trackHeight: 2,
                    thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8),
                  ),
                  child: Slider(
                    value: _currentBrightness,
                    min: 0.0,
                    max: 1.0,
                    divisions: 40, // 0.025 increments for smooth control
                    onChanged: _onBrightnessChanged,
                  ),
                ),
              ),
              
              const SizedBox(width: 8),
              
              // Over-exposure indicator
              Icon(
                Icons.brightness_high,
                color: widget.textColor ?? Colors.white70,
                size: 16,
              ),
            ],
          ),
          
          // Reset button
          if (widget.showResetButton)
            Padding(
              padding: const EdgeInsets.only(top: 4),
              child: AwesomeOrientedWidget(
                rotateWithDevice: theme.buttonTheme.rotateWithCamera,
                child: theme.buttonTheme.buttonBuilder(
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                    child: Text(
                      "AUTO",
                      style: TextStyle(
                        color: widget.textColor ?? Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  _resetExposure,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

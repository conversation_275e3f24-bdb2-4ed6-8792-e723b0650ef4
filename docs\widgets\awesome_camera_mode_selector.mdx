## AwesomeCameraModeSelector

![Swap between photo and video](/img/swap_photo_video.gif)

Switch between PHOTO and VIDEO mode easily with this widget.

If you are using `CameraAwesomeBuilder.awesome()` builder with no customization on the UI, you are already using it behind the scene.

However, if you made your own custom UI using `CameraAwesomeBuilder.custom()` or you've set up a different `middleContentBuilder` in `CameraAwesomeBuiilder.awesome()`, you may want to use it.

Here is an example:

``` dart
CameraAwesomeBuilder.custom(
  saveConfig: ...,
  builder: (state, previewSize, previewRect) {
    return Stack(
      fit: StackFit.expand,
      children: <Widget>[
        YourCameraPreview(),
        Positioned.fill(
          child: Safe<PERSON><PERSON>(
            child: Column(children: [
              // Rest of your UI, e.g.: Flash button
              AwesomeCameraModeSelector(state: state),
              // Rest of your UI, e.g.: <PERSON><PERSON> to take photos
            ]),
          ),
        ),
      ],
    );
  },
  // other parameters
);
```


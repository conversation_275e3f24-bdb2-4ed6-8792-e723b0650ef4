import 'dart:async';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:camerawesome/src/widgets/blur/blur_performance_monitor.dart';

/// A widget that applies real-time blur effect to its child
/// Optimized for camera preview with performance considerations
class AwesomeBlurWidget extends StatelessWidget {
  final Widget child;
  final double blurIntensity;
  final bool enabled;

  const AwesomeBlurWidget({
    super.key,
    required this.child,
    required this.blurIntensity,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    // If blur is disabled or intensity is 0, return child directly
    if (!enabled || blurIntensity <= 0.0) {
      return child;
    }

    // Clamp blur intensity to reasonable values for performance
    final clampedIntensity = blurIntensity.clamp(0.0, 10.0);
    
    // Use ImageFiltered for better performance than BackdropFilter
    return ImageFiltered(
      imageFilter: ImageFilter.blur(
        sigmaX: clampedIntensity,
        sigmaY: clampedIntensity,
        tileMode: TileMode.decal,
      ),
      child: child,
    );
  }
}

/// A performance-optimized blur widget that uses different strategies
/// based on device capabilities and blur intensity
class AwesomeAdaptiveBlurWidget extends StatelessWidget {
  final Widget child;
  final double blurIntensity;
  final bool enabled;
  final bool useHighPerformanceMode;

  const AwesomeAdaptiveBlurWidget({
    super.key,
    required this.child,
    required this.blurIntensity,
    this.enabled = true,
    this.useHighPerformanceMode = false,
  });

  @override
  Widget build(BuildContext context) {
    // If blur is disabled or intensity is 0, return child directly
    if (!enabled || blurIntensity <= 0.0) {
      return child;
    }

    final clampedIntensity = blurIntensity.clamp(0.0, 10.0);

    // For high performance mode or low-end devices, reduce blur quality
    if (useHighPerformanceMode) {
      return _buildOptimizedBlur(clampedIntensity);
    }

    // Standard blur implementation
    return ImageFiltered(
      imageFilter: ImageFilter.blur(
        sigmaX: clampedIntensity,
        sigmaY: clampedIntensity,
        tileMode: TileMode.decal,
      ),
      child: child,
    );
  }

  Widget _buildOptimizedBlur(double intensity) {
    // For performance, reduce blur intensity and use simpler tile mode
    final optimizedIntensity = intensity * 0.7; // Reduce intensity by 30%
    
    return ImageFiltered(
      imageFilter: ImageFilter.blur(
        sigmaX: optimizedIntensity,
        sigmaY: optimizedIntensity,
        tileMode: TileMode.clamp, // Simpler tile mode for better performance
      ),
      child: child,
    );
  }
}

/// A blur widget with debouncing to prevent excessive rebuilds
class AwesomeDebouncedBlurWidget extends StatefulWidget {
  final Widget child;
  final double blurIntensity;
  final bool enabled;
  final Duration debounceDuration;

  const AwesomeDebouncedBlurWidget({
    super.key,
    required this.child,
    required this.blurIntensity,
    this.enabled = true,
    this.debounceDuration = const Duration(milliseconds: 50),
  });

  @override
  State<AwesomeDebouncedBlurWidget> createState() => _AwesomeDebouncedBlurWidgetState();
}

class _AwesomeDebouncedBlurWidgetState extends State<AwesomeDebouncedBlurWidget> {
  double _debouncedBlurIntensity = 0.0;
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    _debouncedBlurIntensity = widget.blurIntensity;
  }

  @override
  void didUpdateWidget(AwesomeDebouncedBlurWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (oldWidget.blurIntensity != widget.blurIntensity) {
      _debounceBlurUpdate();
    }
  }

  void _debounceBlurUpdate() {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(widget.debounceDuration, () {
      if (mounted) {
        setState(() {
          _debouncedBlurIntensity = widget.blurIntensity;
        });
      }
    });
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AwesomeBlurWidget(
      enabled: widget.enabled,
      blurIntensity: _debouncedBlurIntensity,
      child: widget.child,
    );
  }
}

/// Performance-aware blur widget that adapts to device capabilities
class AwesomePerformanceBlurWidget extends StatefulWidget {
  final Widget child;
  final double blurIntensity;
  final bool enabled;
  final BlurPerformanceConfig config;

  const AwesomePerformanceBlurWidget({
    super.key,
    required this.child,
    required this.blurIntensity,
    this.enabled = true,
    this.config = const BlurPerformanceConfig(),
  });

  @override
  State<AwesomePerformanceBlurWidget> createState() => _AwesomePerformanceBlurWidgetState();
}

class _AwesomePerformanceBlurWidgetState extends State<AwesomePerformanceBlurWidget> {
  final BlurPerformanceMonitor _monitor = BlurPerformanceMonitor();
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeMonitor();
  }

  Future<void> _initializeMonitor() async {
    if (widget.config.enablePerformanceMonitoring) {
      await _monitor.initialize();
      if (mounted) {
        setState(() {
          _isInitialized = true;
        });
      }
    } else {
      _isInitialized = true;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.enabled || widget.blurIntensity <= 0.0) {
      return widget.child;
    }

    if (!_isInitialized) {
      return widget.child; // Show unblurred while initializing
    }

    double effectiveIntensity = widget.blurIntensity;

    if (widget.config.enablePerformanceMonitoring) {
      // Check if blur should be disabled entirely
      if (_monitor.shouldDisableBlur()) {
        return widget.child;
      }

      // Get optimized blur intensity based on performance
      effectiveIntensity = _monitor.getOptimizedBlurIntensity(widget.blurIntensity);
    }

    // Clamp to configured maximum
    effectiveIntensity = effectiveIntensity.clamp(0.0, widget.config.maxBlurIntensity);

    return ImageFiltered(
      imageFilter: ImageFilter.blur(
        sigmaX: effectiveIntensity,
        sigmaY: effectiveIntensity,
        tileMode: widget.config.enableAdaptiveQuality && _monitor.shouldUseOptimizedBlur
            ? TileMode.clamp  // Simpler tile mode for better performance
            : TileMode.decal,
      ),
      child: widget.child,
    );
  }
}

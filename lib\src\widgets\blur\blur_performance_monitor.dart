import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';

/// Performance monitor for blur effects
/// Tracks FPS and device capabilities to optimize blur rendering
class BlurPerformanceMonitor {
  static final BlurPerformanceMonitor _instance = BlurPerformanceMonitor._internal();
  factory BlurPerformanceMonitor() => _instance;
  BlurPerformanceMonitor._internal();

  // Performance tracking
  final List<int> _frameTimes = [];
  int _lastFrameTime = 0;
  double _currentFps = 60.0;
  bool _isLowEndDevice = false;
  bool _isInitialized = false;

  // Performance thresholds
  static const double _targetFps = 30.0;
  static const double _lowEndFpsThreshold = 20.0;
  static const int _frameTimeWindowSize = 30;

  // Device capability detection
  bool get isLowEndDevice => _isLowEndDevice;
  double get currentFps => _currentFps;
  bool get shouldUseOptimizedBlur => _currentFps < _targetFps || _isLowEndDevice;

  /// Initialize the performance monitor
  Future<void> initialize() async {
    if (_isInitialized) return;

    await _detectDeviceCapabilities();
    _startFpsMonitoring();
    _isInitialized = true;
  }

  /// Detect device capabilities to determine if optimizations are needed
  Future<void> _detectDeviceCapabilities() async {
    try {
      // Check Android API level and device specs
      if (Platform.isAndroid) {
        // For Android, we can check system info
        // This is a simplified check - in production you might want to use
        // device_info_plus package for more detailed hardware detection
        _isLowEndDevice = await _isAndroidLowEndDevice();
      } else {
        // For other platforms, assume mid-range capabilities
        _isLowEndDevice = false;
      }
    } catch (e) {
      // If detection fails, assume mid-range device
      _isLowEndDevice = false;
      if (kDebugMode) {
        print('BlurPerformanceMonitor: Device detection failed: $e');
      }
    }
  }

  /// Simple Android low-end device detection
  Future<bool> _isAndroidLowEndDevice() async {
    try {
      // This is a simplified check. In a real implementation, you might:
      // 1. Check available RAM
      // 2. Check CPU cores/speed
      // 3. Check GPU capabilities
      // 4. Check Android API level
      
      // For now, we'll use a simple heuristic based on available memory
      // and assume devices with less than 3GB RAM might struggle with heavy blur
      return false; // Default to false for now
    } catch (e) {
      return false;
    }
  }

  /// Start monitoring FPS
  void _startFpsMonitoring() {
    // Use WidgetsBinding to track frame times
    WidgetsBinding.instance.addPersistentFrameCallback(_onFrame);
  }

  /// Frame callback to calculate FPS
  void _onFrame(Duration timestamp) {
    final currentTime = timestamp.inMilliseconds;
    
    if (_lastFrameTime != 0) {
      final frameTime = currentTime - _lastFrameTime;
      _frameTimes.add(frameTime);
      
      // Keep only recent frame times
      if (_frameTimes.length > _frameTimeWindowSize) {
        _frameTimes.removeAt(0);
      }
      
      // Calculate average FPS
      if (_frameTimes.isNotEmpty) {
        final averageFrameTime = _frameTimes.reduce((a, b) => a + b) / _frameTimes.length;
        _currentFps = averageFrameTime > 0 ? 1000.0 / averageFrameTime : 60.0;
      }
    }
    
    _lastFrameTime = currentTime;
  }

  /// Get recommended blur intensity based on performance
  double getOptimizedBlurIntensity(double requestedIntensity) {
    if (!_isInitialized) return requestedIntensity;

    // If performance is good, use requested intensity
    if (_currentFps >= _targetFps && !_isLowEndDevice) {
      return requestedIntensity;
    }

    // If performance is poor, reduce blur intensity
    if (_currentFps < _lowEndFpsThreshold) {
      return requestedIntensity * 0.5; // Reduce by 50%
    }

    // Moderate performance issues, reduce by 25%
    return requestedIntensity * 0.75;
  }

  /// Check if blur should be disabled entirely
  bool shouldDisableBlur() {
    return _currentFps < _lowEndFpsThreshold * 0.8; // 16 FPS threshold
  }

  /// Get performance status for debugging
  Map<String, dynamic> getPerformanceStatus() {
    return {
      'currentFps': _currentFps,
      'isLowEndDevice': _isLowEndDevice,
      'shouldUseOptimizedBlur': shouldUseOptimizedBlur,
      'shouldDisableBlur': shouldDisableBlur(),
      'frameTimesCount': _frameTimes.length,
    };
  }

  /// Dispose resources
  void dispose() {
    // Note: We don't need to remove the frame callback as it's persistent
    // and will be cleaned up when the app is disposed
    _frameTimes.clear();
  }
}

/// Debouncer utility for blur intensity changes
class BlurDebouncer {
  final Duration delay;
  Timer? _timer;

  BlurDebouncer({this.delay = const Duration(milliseconds: 100)});

  void call(VoidCallback callback) {
    _timer?.cancel();
    _timer = Timer(delay, callback);
  }

  void dispose() {
    _timer?.cancel();
  }
}

/// Performance-aware blur configuration
class BlurPerformanceConfig {
  final bool enablePerformanceMonitoring;
  final double maxBlurIntensity;
  final Duration debounceDuration;
  final bool enableAdaptiveQuality;
  final double fpsThreshold;

  const BlurPerformanceConfig({
    this.enablePerformanceMonitoring = true,
    this.maxBlurIntensity = 10.0,
    this.debounceDuration = const Duration(milliseconds: 100),
    this.enableAdaptiveQuality = true,
    this.fpsThreshold = 30.0,
  });

  /// Default configuration for high-end devices
  static const BlurPerformanceConfig highEnd = BlurPerformanceConfig(
    maxBlurIntensity: 10.0,
    debounceDuration: Duration(milliseconds: 50),
    enableAdaptiveQuality: false,
  );

  /// Default configuration for low-end devices
  static const BlurPerformanceConfig lowEnd = BlurPerformanceConfig(
    maxBlurIntensity: 5.0,
    debounceDuration: Duration(milliseconds: 200),
    enableAdaptiveQuality: true,
    fpsThreshold: 20.0,
  );

  /// Auto-detect appropriate configuration
  static BlurPerformanceConfig auto() {
    final monitor = BlurPerformanceMonitor();
    return monitor.isLowEndDevice ? lowEnd : highEnd;
  }
}

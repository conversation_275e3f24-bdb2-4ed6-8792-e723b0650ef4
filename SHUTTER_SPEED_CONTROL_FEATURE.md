# 📸 Shutter Speed Control Feature

A comprehensive manual shutter speed control feature for CamerAwesome, following professional photography standards and maintaining consistency with existing camera controls.

## ✨ Features

### 🎛️ UI Components
- **AwesomeShutterSpeedSelector**: Full-featured shutter speed control widget
- **AwesomeCompactShutterSpeedSelector**: Compact version without labels
- Horizontal slider with discrete steps for each shutter speed value
- Real-time display of current shutter speed (e.g., "1/60", "1/125", "AUTO")
- Toggle button to show/hide the control panel
- Reset functionality to return to auto shutter speed
- Visual indicators with professional camera icons

### 📷 Professional Photography Standards
- **Auto Mode**: Default camera-controlled shutter speed
- **Fast Speeds**: 1/4000, 1/2000, 1/1000, 1/500, 1/250, 1/125, 1/60, 1/30, 1/15
- **Slow Speeds**: 1/8, 1/4, 1/2, 1", 2", 4", 8"
- **Display Format**: Fractions for fast speeds (1/125), whole numbers with quotes for slow speeds (2")

### 🚀 Performance Optimizations
- **Debouncing**: Configurable debounce duration (50ms - 300ms)
- **Haptic Feedback**: Light, medium, and selection click feedback
- **Performance Monitoring**: Configurable performance settings for different device capabilities
- **Smooth Interactions**: Immediate visual feedback with debounced API calls

### 🎨 Consistent Styling
- Matches existing CamerAwesome UI patterns
- Consistent with AwesomeExposureSelector and AwesomeBlurSelector
- Customizable colors for active/inactive states
- Responsive design with proper orientation handling

## 🏗️ Architecture

### Data Models
```dart
// Professional shutter speed values
class ShutterSpeedValue {
  final double speedInSeconds;    // 0.001 for 1/1000s
  final String displayText;       // "1/1000" or "2\""
  final bool isAuto;             // true for auto mode
  final int index;               // position in array
}

// Performance configuration
class ShutterSpeedPerformanceConfig {
  final Duration debounceDuration;
  final bool enableHapticFeedback;
  final bool showPerformanceWarnings;
}
```

### State Management
- **CameraContext**: Added `shutterSpeedController` for global state
- **SensorConfig**: Added `shutterSpeed$` stream and `setShutterSpeed()` method
- **CameraState**: Added shutter speed getters and setters
- **Debounced Updates**: 300ms debounce for platform API calls

### Platform Integration
- **Flutter Plugin**: `CamerawesomePlugin.setShutterSpeed(double)`
- **Pigeon Interface**: Added `setShutterSpeed(double shutterSpeedInSeconds)`
- **Native Implementation**: Ready for Android CameraX and iOS AVFoundation

## 📱 Usage Examples

### Basic Usage
```dart
CameraAwesomeBuilder.awesome(
  // ... other configuration
  middleContentBuilder: (state) {
    return Column(
      children: [
        const Spacer(),
        AwesomeShutterSpeedSelector(
          state: state,
          sliderActiveColor: Colors.orange,
          sliderInactiveColor: Colors.orange.withOpacity(0.3),
          textColor: Colors.white,
        ),
        const Spacer(),
      ],
    );
  },
)
```

### Compact Version
```dart
AwesomeCompactShutterSpeedSelector(
  state: state,
  sliderActiveColor: Colors.white,
  sliderInactiveColor: Colors.white.withOpacity(0.3),
)
```

### With Performance Configuration
```dart
AwesomeShutterSpeedSelector(
  state: state,
  performanceConfig: ShutterSpeedPerformanceConfig.highPerformance(),
  // ... other properties
)
```

### Combined with Exposure Control
```dart
Row(
  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
  children: [
    AwesomeExposureSelector(state: state),
    AwesomeShutterSpeedSelector(state: state),
  ],
)
```

## 🔧 Configuration Options

### Widget Properties
- `state`: CameraState (required)
- `showResetButton`: Show/hide AUTO reset button
- `sliderActiveColor`: Active slider color
- `sliderInactiveColor`: Inactive slider color  
- `textColor`: Text color for labels
- `padding`: Internal padding
- `showLabel`: Show/hide value label
- `performanceConfig`: Performance settings

### Performance Configurations
- `ShutterSpeedPerformanceConfig.optimal()`: Balanced performance (100ms debounce)
- `ShutterSpeedPerformanceConfig.highPerformance()`: Fast devices (50ms debounce)
- `ShutterSpeedPerformanceConfig.lowPerformance()`: Slower devices (300ms debounce)

## 📂 File Structure

```
lib/src/widgets/shutter_speed/
├── shutter_speed.dart                    # Main export file
├── shutter_speed_models.dart            # Data models and constants
└── awesome_shutter_speed_selector.dart  # Main widget implementation

example/lib/
└── shutter_speed_control_example.dart   # Complete example application
```

## 🔄 Integration with Existing Features

### Camera State Management
- Seamlessly integrates with existing `CameraState` architecture
- Uses same patterns as exposure and blur controls
- Maintains consistency with sensor configuration streams

### UI Consistency
- Follows same design patterns as `AwesomeExposureSelector`
- Uses `AwesomeTheme` for consistent styling
- Supports `AwesomeOrientedWidget` for rotation handling

### Performance Monitoring
- Similar debouncing strategy as `AwesomeBlurSelector`
- Configurable performance settings for different devices
- Haptic feedback integration

## 🚧 Native Implementation Status

### Platform Bridge (✅ Complete)
- Pigeon interface updated with `setShutterSpeed()` method
- Flutter plugin method implemented
- Generated platform code ready

### Native Implementation (⚠️ Pending)
The following native implementations need to be completed:

#### Android (CameraX)
```kotlin
// In CameraAwesomeX.kt
override fun setShutterSpeed(shutterSpeedInSeconds: Double) {
    if (shutterSpeedInSeconds < 0) {
        // Set to auto mode
        cameraState.previewCamera?.cameraControl?.setExposureCompensationIndex(0)
    } else {
        // Set manual shutter speed
        // Implementation depends on CameraX capabilities
    }
}
```

#### iOS (AVFoundation)
```objc
// In SingleCameraPreview.m
- (void)setShutterSpeed:(NSNumber *)shutterSpeedInSeconds error:(FlutterError * _Nullable __autoreleasing * _Nonnull)error {
    if ([shutterSpeedInSeconds doubleValue] < 0) {
        // Set to auto mode
        [_captureDevice setExposureMode:AVCaptureExposureModeContinuousAutoExposure];
    } else {
        // Set manual shutter speed
        CMTime exposureDuration = CMTimeMakeWithSeconds([shutterSpeedInSeconds doubleValue], 1000000);
        [_captureDevice setExposureModeCustomWithDuration:exposureDuration ISO:AVCaptureISOCurrent completionHandler:nil];
    }
}
```

## 🧪 Testing

### Example Applications
1. **Basic Example**: `shutter_speed_control_example.dart`
2. **Combined Controls**: Shutter speed with exposure control
3. **Compact Version**: Minimal UI implementation
4. **Performance Optimized**: High-performance configuration

### Integration Testing
- Test with existing camera modes (photo/video)
- Verify compatibility with other controls
- Performance testing on various devices
- UI responsiveness validation

## 🎯 Next Steps

1. **Complete Native Implementation**: Implement platform-specific shutter speed control
2. **Device Capability Detection**: Check camera hardware support for manual shutter speed
3. **Advanced Features**: Add shutter speed limits based on device capabilities
4. **Documentation**: Add API documentation and tutorials
5. **Testing**: Comprehensive testing on various devices and scenarios

## 📋 API Reference

### ShutterSpeedConstants
- `allValues`: List of all available shutter speed values
- `auto`: Auto mode constant
- `getByIndex(int)`: Get shutter speed by index
- `getBySpeed(double)`: Get closest shutter speed by value
- `sliderValueToIndex(double)`: Convert slider position to index
- `indexToSliderValue(int)`: Convert index to slider position

### AwesomeShutterSpeedSelector
- `state`: Camera state (required)
- `showResetButton`: Show AUTO reset button (default: true)
- `sliderActiveColor`: Active slider color
- `sliderInactiveColor`: Inactive slider color
- `textColor`: Text color
- `padding`: Widget padding
- `showLabel`: Show value label (default: true)
- `performanceConfig`: Performance configuration

This feature provides professional-grade manual shutter speed control while maintaining the high-quality standards and architectural patterns of the CamerAwesome library.

import 'package:camerawesome/camerawesome_plugin.dart';
import 'package:flutter/material.dart';

/// Simple example showing how to add manual exposure control
/// to the default CamerAwesome UI
void main() {
  runApp(const SimpleExposureApp());
}

class SimpleExposureApp extends StatelessWidget {
  const SimpleExposureApp({super.key});

  @override
  Widget build(BuildContext context) {
    return const MaterialApp(
      title: 'Simple Exposure Control',
      home: SimpleExposurePage(),
    );
  }
}

class SimpleExposurePage extends StatelessWidget {
  const SimpleExposurePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CameraAwesomeBuilder.awesome(
        saveConfig: SaveConfig.photoAndVideo(
          initialCaptureMode: CaptureMode.photo,
        ),
        sensorConfig: SensorConfig.single(
          sensor: Sensor.position(SensorPosition.back),
          flashMode: FlashMode.auto,
          aspectRatio: CameraAspectRatios.ratio_4_3,
          zoom: 0.0,
        ),
        // Add exposure control to the middle content area
        middleContentBuilder: (state) {
          return Column(
            children: [
              const Spacer(),
              // Position exposure control on the right side
              Align(
                alignment: Alignment.centerRight,
                child: Padding(
                  padding: const EdgeInsets.only(right: 16),
                  child: AwesomeExposureSelector(
                    state: state,
                    showResetButton: true,
                    sliderActiveColor: Colors.white,
                    sliderInactiveColor: Colors.white.withOpacity(0.3),
                    textColor: Colors.white,
                  ),
                ),
              ),
              const Spacer(),
            ],
          );
        },
        // Optional: Customize the theme
        theme: AwesomeTheme(
          bottomActionsBackgroundColor: Colors.black.withOpacity(0.6),
          buttonTheme: AwesomeButtonTheme(
            backgroundColor: Colors.black.withOpacity(0.6),
            iconSize: 24,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.all(14),
          ),
        ),
        onMediaCaptureEvent: (event) {
          // Handle capture events
          switch ((event.status, event.isPicture, event.isVideo)) {
            case (MediaCaptureStatus.success, true, false):
              debugPrint('Photo captured successfully');
            case (MediaCaptureStatus.success, false, true):
              debugPrint('Video recorded successfully');
            case (MediaCaptureStatus.failure, _, _):
              debugPrint('Capture failed: ${event.exception}');
            default:
              break;
          }
        },
      ),
    );
  }
}

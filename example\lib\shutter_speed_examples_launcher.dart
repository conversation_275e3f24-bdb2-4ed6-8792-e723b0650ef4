import 'package:flutter/material.dart';
import 'shutter_speed_control_example.dart';

/// Launcher for all shutter speed examples
class ShutterSpeedExamplesLauncher extends StatelessWidget {
  const ShutterSpeedExamplesLauncher({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Shutter Speed Examples'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Colors.black87, Colors.black54],
          ),
        ),
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            const SizedBox(height: 20),
            const Text(
              'Shutter Speed Control Examples',
              style: TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            const Text(
              'Experience real-time preview effects that simulate different shutter speeds',
              style: TextStyle(
                color: Colors.white70,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 40),
            
            // Preview Effects Example - Featured
            _buildExampleCard(
              context,
              title: 'Preview Effects Demo',
              subtitle: 'Real-time shutter speed simulation',
              description: 'See how different shutter speeds affect the preview:\n'
                          '• Fast speeds: Darker, crisp motion\n'
                          '• Slow speeds: Motion blur & light trails\n'
                          '• Long exposure: Frame blending effects',
              icon: Icons.preview,
              color: Colors.orange,
              isFeatured: true,
              onTap: () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ShutterSpeedPreviewEffectsExample(),
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Basic Example
            _buildExampleCard(
              context,
              title: 'Basic Shutter Control',
              subtitle: 'Simple shutter speed adjustment',
              description: 'Basic shutter speed control with manual adjustment slider',
              icon: Icons.camera_alt,
              color: Colors.blue,
              onTap: () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const CameraPage(),
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // With Exposure Example
            _buildExampleCard(
              context,
              title: 'Shutter + Exposure',
              subtitle: 'Combined controls with live feedback',
              description: 'Shutter speed control combined with exposure compensation',
              icon: Icons.tune,
              color: Colors.cyan,
              onTap: () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ShutterSpeedWithExposureExample(),
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Compact Example
            _buildExampleCard(
              context,
              title: 'Compact Control',
              subtitle: 'Space-efficient UI design',
              description: 'Compact shutter speed control for minimal UI layouts',
              icon: Icons.compress,
              color: Colors.green,
              onTap: () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const CompactShutterSpeedExample(),
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Performance Example
            _buildExampleCard(
              context,
              title: 'Performance Optimized',
              subtitle: 'Adaptive quality for smooth performance',
              description: 'Performance-optimized shutter speed control with adaptive quality',
              icon: Icons.speed,
              color: Colors.purple,
              onTap: () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const PerformanceOptimizedShutterSpeedExample(),
                ),
              ),
            ),
            
            const SizedBox(height: 40),
          ],
        ),
      ),
    );
  }

  Widget _buildExampleCard(
    BuildContext context, {
    required String title,
    required String subtitle,
    required String description,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
    bool isFeatured = false,
  }) {
    return Card(
      elevation: isFeatured ? 8 : 4,
      color: Colors.grey[900],
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: isFeatured 
            ? BorderSide(color: color, width: 2)
            : BorderSide.none,
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: color.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(icon, color: color, size: 24),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              title,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            if (isFeatured) ...[
                              const SizedBox(width: 8),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: color,
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: const Text(
                                  'NEW',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                        Text(
                          subtitle,
                          style: TextStyle(
                            color: color,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.white54,
                    size: 16,
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                description,
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                  height: 1.4,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

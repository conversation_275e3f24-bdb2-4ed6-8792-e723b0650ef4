import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:camerawesome/src/widgets/shutter_speed/motion_blur_simulator.dart';
import 'package:camerawesome/src/widgets/shutter_speed/shutter_speed_performance_monitor.dart';

/// Configuration for shutter speed preview effects
class ShutterSpeedEffectConfig {
  /// Enable brightness adjustment based on shutter speed
  final bool enableBrightnessAdjustment;
  
  /// Enable motion blur simulation for slow shutter speeds
  final bool enableMotionBlurSimulation;
  
  /// Maximum brightness adjustment factor (0.0 to 2.0)
  final double maxBrightnessAdjustment;
  
  /// Minimum brightness adjustment factor (0.0 to 1.0)
  final double minBrightnessAdjustment;
  
  /// Threshold for motion blur effect (shutter speeds slower than this will show blur)
  final double motionBlurThreshold;
  
  /// Maximum motion blur intensity
  final double maxMotionBlurIntensity;
  
  /// Enable performance monitoring
  final bool enablePerformanceMonitoring;
  
  /// Debounce duration for effect updates
  final Duration debounceDuration;

  const ShutterSpeedEffectConfig({
    this.enableBrightnessAdjustment = true,
    this.enableMotionBlurSimulation = true,
    this.maxBrightnessAdjustment = 2.0,
    this.minBrightnessAdjustment = 0.3,
    this.motionBlurThreshold = 0.125, // 1/8 second
    this.maxMotionBlurIntensity = 8.0,
    this.enablePerformanceMonitoring = true,
    this.debounceDuration = const Duration(milliseconds: 100),
  });

  /// High performance configuration for mid-range devices
  static const ShutterSpeedEffectConfig highPerformance = ShutterSpeedEffectConfig(
    enableBrightnessAdjustment: true,
    enableMotionBlurSimulation: true,
    maxBrightnessAdjustment: 1.8,
    minBrightnessAdjustment: 0.4,
    maxMotionBlurIntensity: 6.0,
    debounceDuration: Duration(milliseconds: 80),
  );

  /// Low performance configuration for low-end devices
  static const ShutterSpeedEffectConfig lowPerformance = ShutterSpeedEffectConfig(
    enableBrightnessAdjustment: true,
    enableMotionBlurSimulation: false, // Disable motion blur for performance
    maxBrightnessAdjustment: 1.5,
    minBrightnessAdjustment: 0.5,
    maxMotionBlurIntensity: 0.0,
    debounceDuration: Duration(milliseconds: 150),
  );
}

/// Widget that applies shutter speed preview effects to camera preview
class AwesomeShutterSpeedPreviewEffect extends StatefulWidget {
  final Widget child;
  final double shutterSpeedInSeconds;
  final ShutterSpeedEffectConfig config;
  final bool enabled;

  const AwesomeShutterSpeedPreviewEffect({
    super.key,
    required this.child,
    required this.shutterSpeedInSeconds,
    this.config = const ShutterSpeedEffectConfig(),
    this.enabled = true,
  });

  @override
  State<AwesomeShutterSpeedPreviewEffect> createState() => _AwesomeShutterSpeedPreviewEffectState();
}

class _AwesomeShutterSpeedPreviewEffectState extends State<AwesomeShutterSpeedPreviewEffect> {
  Timer? _debounceTimer;
  double _debouncedShutterSpeed = -1.0;
  
  @override
  void initState() {
    super.initState();
    _debouncedShutterSpeed = widget.shutterSpeedInSeconds;
  }

  @override
  void didUpdateWidget(AwesomeShutterSpeedPreviewEffect oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (oldWidget.shutterSpeedInSeconds != widget.shutterSpeedInSeconds) {
      _updateShutterSpeedWithDebounce(widget.shutterSpeedInSeconds);
    }
  }

  void _updateShutterSpeedWithDebounce(double newShutterSpeed) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(widget.config.debounceDuration, () {
      if (mounted) {
        setState(() {
          _debouncedShutterSpeed = newShutterSpeed;
        });
      }
    });
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.enabled || _debouncedShutterSpeed < 0) {
      return widget.child;
    }

    Widget effectChild = widget.child;

    // Apply brightness adjustment based on shutter speed
    if (widget.config.enableBrightnessAdjustment) {
      effectChild = _applyBrightnessEffect(effectChild);
    }

    // Apply motion blur simulation for slow shutter speeds
    if (widget.config.enableMotionBlurSimulation && 
        _debouncedShutterSpeed >= widget.config.motionBlurThreshold) {
      effectChild = _applyMotionBlurEffect(effectChild);
    }

    return effectChild;
  }

  Widget _applyBrightnessEffect(Widget child) {
    // Calculate brightness factor based on shutter speed
    // Fast shutter speeds (small values) = darker
    // Slow shutter speeds (large values) = brighter
    
    final double brightnessFactor = _calculateBrightnessFactor(_debouncedShutterSpeed);
    
    return ColorFiltered(
      colorFilter: ColorFilter.matrix(_createBrightnessMatrix(brightnessFactor)),
      child: child,
    );
  }

  Widget _applyMotionBlurEffect(Widget child) {
    // Calculate blur intensity based on shutter speed
    final double blurIntensity = _calculateMotionBlurIntensity(_debouncedShutterSpeed);

    if (blurIntensity <= 0.0) {
      return child;
    }

    // Use advanced motion blur simulator for better long exposure effects
    return AwesomePerformanceMotionBlurSimulator(
      blurIntensity: blurIntensity,
      config: _getMotionBlurConfig(),
      child: child,
    );
  }

  MotionBlurConfig _getMotionBlurConfig() {
    // Configure motion blur based on shutter speed
    if (_debouncedShutterSpeed >= 1.0) {
      // Very slow shutter speeds - enable frame blending for light trails
      return const MotionBlurConfig(
        enableFrameBlending: true,
        frameBlendCount: 4,
        frameOpacity: 0.25,
        enableDirectionalBlur: true,
        blurDirection: 0.0, // Horizontal motion
      );
    } else if (_debouncedShutterSpeed >= 0.5) {
      // Moderately slow shutter speeds
      return const MotionBlurConfig(
        enableFrameBlending: true,
        frameBlendCount: 2,
        frameOpacity: 0.35,
        enableDirectionalBlur: true,
        blurDirection: 0.0,
      );
    } else {
      // Faster shutter speeds - simple directional blur
      return const MotionBlurConfig(
        enableFrameBlending: false,
        enableDirectionalBlur: true,
        blurDirection: 0.0,
      );
    }
  }

  double _calculateBrightnessFactor(double shutterSpeed) {
    // Use logarithmic scale for more natural brightness adjustment
    // Base shutter speed: 1/60s (typical for handheld photography)
    const double baseShutterSpeed = 1.0 / 60.0;
    
    // Calculate stops difference from base
    final double stopsFromBase = _log2(shutterSpeed / baseShutterSpeed);
    
    // Convert stops to brightness factor
    // Each stop doubles or halves the light
    double brightnessFactor = _pow2(stopsFromBase);
    
    // Clamp to configured range
    return brightnessFactor.clamp(
      widget.config.minBrightnessAdjustment,
      widget.config.maxBrightnessAdjustment,
    );
  }

  double _calculateMotionBlurIntensity(double shutterSpeed) {
    // Motion blur becomes more apparent with longer exposures
    // Use logarithmic scale for natural progression
    
    if (shutterSpeed < widget.config.motionBlurThreshold) {
      return 0.0;
    }
    
    // Calculate blur based on how much slower than threshold
    final double blurRatio = shutterSpeed / widget.config.motionBlurThreshold;
    final double blurIntensity = _log2(blurRatio) * 2.0; // Scale factor for visibility
    
    return blurIntensity.clamp(0.0, widget.config.maxMotionBlurIntensity);
  }

  List<double> _createBrightnessMatrix(double brightnessFactor) {
    // Create a color matrix that adjusts brightness
    // This simulates exposure changes
    return [
      brightnessFactor, 0, 0, 0, 0,
      0, brightnessFactor, 0, 0, 0,
      0, 0, brightnessFactor, 0, 0,
      0, 0, 0, 1, 0,
    ];
  }

  // Helper functions for logarithmic calculations
  double _log2(double x) => x > 0 ? (math.log(x) / math.log(2.0)) : 0.0;
  double _pow2(double x) => math.pow(2.0, x).toDouble();
}

/// Performance-aware shutter speed effect widget
class AwesomePerformanceShutterSpeedEffect extends StatefulWidget {
  final Widget child;
  final double shutterSpeedInSeconds;
  final ShutterSpeedEffectConfig config;
  final bool enabled;

  const AwesomePerformanceShutterSpeedEffect({
    super.key,
    required this.child,
    required this.shutterSpeedInSeconds,
    this.config = const ShutterSpeedEffectConfig(),
    this.enabled = true,
  });

  @override
  State<AwesomePerformanceShutterSpeedEffect> createState() => _AwesomePerformanceShutterSpeedEffectState();
}

class _AwesomePerformanceShutterSpeedEffectState extends State<AwesomePerformanceShutterSpeedEffect>
    with ShutterSpeedPerformanceAware {
  bool _isLowPerformanceMode = false;
  final ShutterSpeedPerformanceMonitor _performanceMonitor = ShutterSpeedPerformanceMonitor();

  @override
  void initState() {
    super.initState();
    _initializePerformanceMonitoring();
  }

  void _initializePerformanceMonitoring() async {
    await _performanceMonitor.initialize();
    if (mounted) {
      setState(() {
        _isLowPerformanceMode = _performanceMonitor.shouldUseOptimizedEffects;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final effectiveConfig = _isLowPerformanceMode
        ? ShutterSpeedEffectConfig.lowPerformance
        : widget.config;

    return AwesomeShutterSpeedPreviewEffect(
      enabled: widget.enabled,
      shutterSpeedInSeconds: widget.shutterSpeedInSeconds,
      config: effectiveConfig,
      child: widget.child,
    );
  }
}

# Real-time Adjustable Blur Preview Feature

## Overview

The real-time adjustable blur preview feature allows users to apply and adjust blur effects to the camera preview in real-time. This feature is designed to work across all Android devices, including those with Camera2 Legacy support, while maintaining optimal performance.

## Features

### Core Functionality
- **Real-time blur preview**: Apply blur effects to camera preview with immediate visual feedback
- **Adjustable intensity**: Blur intensity range from 0.0 (no blur) to 10.0 (maximum blur)
- **Performance optimization**: Automatic performance monitoring and adaptive quality adjustment
- **Device compatibility**: Works on all Android API levels including Camera2 Legacy devices
- **Software-based implementation**: Uses Flutter's ImageFilter.blur for consistent cross-device performance

### UI Components
- **Horizontal slider**: Intuitive slider control for adjusting blur intensity
- **Real-time value display**: Shows current blur intensity value
- **Quick preset buttons**: Fast access to common blur levels (Off, Low, Medium, High, Max)
- **Visual indicators**: Icons showing blur off/on states

### Performance Features
- **Debouncing**: Prevents excessive processing during rapid slider changes
- **FPS monitoring**: Tracks frame rate and adjusts blur quality automatically
- **Device capability detection**: Automatically detects low-end devices and optimizes accordingly
- **Graceful degradation**: Reduces blur quality or disables feature on very low-end devices

## Implementation Architecture

### State Management
The blur feature integrates with CamerAwesome's existing state management system:

```dart
// Blur intensity is managed through CameraContext
Stream<double> get blur$ => cameraContext.blur$;
double get blurIntensity => cameraContext.blurController.value;

// Set blur intensity
Future<void> setBlurIntensity(double intensity) => 
    cameraContext.setBlurIntensity(intensity);
```

### Preview Rendering Pipeline
Blur is applied in the camera preview rendering pipeline:

1. Camera texture is rendered
2. Color filters are applied (if any)
3. Blur effect is applied on top using ImageFiltered widget
4. Final preview is displayed

### Performance Monitoring
The `BlurPerformanceMonitor` class tracks:
- Frame rate (FPS)
- Device capabilities
- Performance thresholds
- Automatic quality adjustments

## Usage Examples

### Basic Usage
```dart
CameraAwesomeBuilder.awesome(
  // ... other configuration
  middleContentBuilder: (state) {
    return Column(
      children: [
        const Spacer(),
        if (state is PhotoCameraState)
          AwesomeBlurSelector(state: state),
        AwesomeCameraModeSelector(state: state),
      ],
    );
  },
)
```

### Custom Blur Selector
```dart
AwesomeBlurSelector(
  state: state,
  sliderActiveColor: Colors.blue,
  sliderInactiveColor: Colors.grey,
  showLabel: true,
  debounceDuration: Duration(milliseconds: 100),
  performanceConfig: BlurPerformanceConfig.auto(),
)
```

### Performance-Aware Configuration
```dart
// High-end device configuration
const BlurPerformanceConfig.highEnd = BlurPerformanceConfig(
  maxBlurIntensity: 10.0,
  debounceDuration: Duration(milliseconds: 50),
  enableAdaptiveQuality: false,
);

// Low-end device configuration
const BlurPerformanceConfig.lowEnd = BlurPerformanceConfig(
  maxBlurIntensity: 5.0,
  debounceDuration: Duration(milliseconds: 200),
  enableAdaptiveQuality: true,
  fpsThreshold: 20.0,
);
```

## Performance Characteristics

### Target Performance
- **30+ FPS** on mid-range devices during blur preview
- **Graceful degradation** on low-end devices
- **Automatic optimization** based on real-time performance monitoring

### Optimization Strategies
1. **Debouncing**: Slider changes are debounced to prevent excessive processing
2. **Adaptive quality**: Blur quality is reduced when performance drops
3. **Intensity scaling**: Blur intensity is automatically reduced on low-end devices
4. **Tile mode optimization**: Uses simpler tile modes for better performance when needed

### Device Compatibility
- **Android API 21+**: Full support with all features
- **Camera2 Legacy**: Fully compatible with legacy camera implementations
- **Low-end devices**: Automatic detection and optimization
- **Mid-range devices**: Full feature set with optimal performance
- **High-end devices**: Maximum quality and performance

## Technical Implementation Details

### Blur Widget Hierarchy
```
AwesomePerformanceBlurWidget
├── BlurPerformanceMonitor (performance tracking)
├── ImageFiltered (blur application)
└── Child widget (camera preview)
```

### State Flow
```
User adjusts slider → Debounced update → State change → 
Stream notification → Widget rebuild → Blur applied
```

### Performance Monitoring Flow
```
Frame callback → FPS calculation → Performance assessment → 
Quality adjustment → Blur intensity optimization
```

## Testing and Validation

### Test Coverage
- ✅ Real-time blur application
- ✅ Performance monitoring
- ✅ Device compatibility
- ✅ State management
- ✅ UI responsiveness
- ✅ Memory usage optimization

### Validation Checklist
- [x] Works on Camera2 Legacy devices
- [x] Maintains 30+ FPS on mid-range devices
- [x] Graceful degradation on low-end devices
- [x] No interference with other camera features
- [x] Proper state management and cleanup
- [x] Responsive UI controls

## Future Enhancements

### Potential Improvements
1. **Blur shapes**: Support for different blur shapes (circular, linear)
2. **Selective blur**: Apply blur to specific regions of the preview
3. **Blur presets**: Predefined blur configurations for different scenarios
4. **Background blur**: Automatic background detection and selective blurring
5. **Performance analytics**: Detailed performance reporting and optimization suggestions

### API Extensions
```dart
// Future API possibilities
state.setBlurRegion(Rect region, double intensity);
state.setBlurShape(BlurShape.circular);
state.enableBackgroundBlur(true);
```

## Troubleshooting

### Common Issues
1. **Performance drops**: Check device capabilities and enable adaptive quality
2. **Blur not visible**: Ensure blur intensity > 0.0 and feature is enabled
3. **UI lag**: Increase debounce duration or reduce maximum blur intensity
4. **Memory issues**: Monitor performance and enable automatic optimization

### Debug Information
```dart
// Get performance status
final monitor = BlurPerformanceMonitor();
final status = monitor.getPerformanceStatus();
print('FPS: ${status['currentFps']}');
print('Low-end device: ${status['isLowEndDevice']}');
```

This implementation provides a robust, performant, and user-friendly blur feature that enhances the camera experience while maintaining compatibility across all Android devices.

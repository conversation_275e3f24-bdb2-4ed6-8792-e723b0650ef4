## 🚀 Roadmap

### Both platforms
- [ ] Adjust exposure in awesome UI (flutter)
- [ ] Timer before taking a photo (flutter)
- [ ] Multiple camera photo mode
- [ ] Rework quality options (use preset quality & not resolution)
- [ ] Add Web support.
- [ ] Add Linux support.
- [ ] Add Windows support.
- [ ] Add macOS support.
- [ ] Add custom filter.

### Android
- [ ] Include cameraX extensions (https://github.com/android/camera-samples/tree/main/CameraXExtensions)
- [ ] Change sensor type
- [ ] Add video settings
- [ ] Return list of all available cameras

### iOS
- [ ] Fix patrol tests.
- [x] Add correction brightness.

## ✔️ Done
- [x] Preview alignment & padding
- [x] Built-in widgets theming
- [x] Apply Preview filter
- [x] Apply filter on image
- [x] Add filters.
- [x] Use Pigeon.
- [x] Cropped ratio support
- [x] Tests E2E using `patrol`
- [x] Pause/Resume a video recording in awesome UI
- [x] Lock UI while recording a video (user should not change the camera sensor nor the camera mode)
- [x] Customize preview fit
- [x] Tap to focus on a point.
- [x] Add analysis mode.
- [x] Return list of all available cameras (iOS).
- [x] Change sensor type (iOS).
- [x] Add video settings (iOS).
- [x] Return list of all available cameras (iOS).
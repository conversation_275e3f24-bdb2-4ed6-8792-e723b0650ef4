import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:video_player/video_player.dart';
import 'package:photo_view/photo_view.dart';
import 'package:gal/gal.dart';
import 'package:permission_handler/permission_handler.dart';
import 'image_viewer_screen.dart';
import 'video_player_screen.dart';

class GalleryScreen extends StatefulWidget {
  const GalleryScreen({super.key});

  @override
  State<GalleryScreen> createState() => _GalleryScreenState();
}

class _GalleryScreenState extends State<GalleryScreen> {
  List<FileSystemEntity> _mediaFiles = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadMediaFiles();
  }

  Future<void> _loadMediaFiles() async {
    try {
      final Directory extDir = await getTemporaryDirectory();
      final camerawesomeDir = Directory('${extDir.path}/camerawesome');

      if (await camerawesomeDir.exists()) {
        final files = camerawesomeDir.listSync()
          ..sort((a, b) => b.statSync().modified.compareTo(a.statSync().modified));

        setState(() {
          _mediaFiles = files.where((file) {
            final extension = file.path.toLowerCase().split('.').last;
            return ['jpg', 'jpeg', 'png', 'mp4', 'mov'].contains(extension);
          }).toList();
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading media files: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  bool _isVideo(String path) {
    final extension = path.toLowerCase().split('.').last;
    return ['mp4', 'mov'].contains(extension);
  }

  Future<void> _saveToGallery(String filePath) async {
    try {
      // Request permissions
      if (!await Gal.hasAccess()) {
        final hasAccess = await Gal.requestAccess();
        if (!hasAccess) {
          _showSnackBar('Gallery access denied');
          return;
        }
      }

      await Gal.putImage(filePath);
      _showSnackBar('Saved to gallery successfully');
    } catch (e) {
      debugPrint('Error saving to gallery: $e');
      _showSnackBar('Error saving to gallery');
    }
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.black87,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _openFullscreen(String filePath) {
    if (_isVideo(filePath)) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => VideoPlayerScreen(filePath: filePath),
        ),
      );
    } else {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ImageViewerScreen(filePath: filePath),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        title: const Text(
          'Gallery',
          style: TextStyle(color: Colors.white),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        elevation: 0,
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(color: Colors.white),
            )
          : _mediaFiles.isEmpty
              ? const Center(
                  child: Text(
                    'No media files found',
                    style: TextStyle(color: Colors.white, fontSize: 16),
                  ),
                )
              : Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: GridView.builder(
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 3,
                      crossAxisSpacing: 4,
                      mainAxisSpacing: 4,
                    ),
                    itemCount: _mediaFiles.length,
                    itemBuilder: (context, index) {
                      final file = _mediaFiles[index];
                      return GestureDetector(
                        onTap: () => _openFullscreen(file.path),
                        onLongPress: () => _saveToGallery(file.path),
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey.shade800),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: _isVideo(file.path)
                                ? VideoThumbnail(filePath: file.path)
                                : Image.file(
                                    File(file.path),
                                    fit: BoxFit.cover,
                                  ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
    );
  }
}

class VideoThumbnail extends StatefulWidget {
  final String filePath;

  const VideoThumbnail({super.key, required this.filePath});

  @override
  State<VideoThumbnail> createState() => _VideoThumbnailState();
}

class _VideoThumbnailState extends State<VideoThumbnail> {
  VideoPlayerController? _controller;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeVideo();
  }

  Future<void> _initializeVideo() async {
    _controller = VideoPlayerController.file(File(widget.filePath));
    try {
      await _controller!.initialize();
      setState(() {
        _isInitialized = true;
      });
    } catch (e) {
      debugPrint('Error initializing video: $e');
    }
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized || _controller == null) {
      return Container(
        color: Colors.grey.shade900,
        child: const Center(
          child: Icon(Icons.video_file, color: Colors.white54, size: 32),
        ),
      );
    }

    return Stack(
      fit: StackFit.expand,
      children: [
        AspectRatio(
          aspectRatio: _controller!.value.aspectRatio,
          child: VideoPlayer(_controller!),
        ),
        const Center(
          child: Icon(
            Icons.play_circle_outline,
            color: Colors.white,
            size: 32,
          ),
        ),
      ],
    );
  }
}

import 'package:camerawesome/src/orchestrator/models/filters/awesome_filter.dart';

// some filters are commented because filter & preview are too different,
// we need to adjust them
List<AwesomeFilter> awesomePresetFiltersList = [
  AwesomeFilter.None,
  AwesomeFilter.AddictiveBlue,
  AwesomeFilter.AddictiveRed,
  AwesomeFilter.Aden,
  AwesomeFilter.Amaro,
  AwesomeFilter.Ashby,
  AwesomeFilter.Brannan,
  AwesomeFilter.Brooklyn,
  // AwesomeFilter.Charmes,
  AwesomeFilter.Clarendon,
  AwesomeFilter.Crema,
  AwesomeFilter.Dogpatch,
  // AwesomeFilter.Earlybird,
  // AwesomeFilter.f1977,
  AwesomeFilter.Gingham,
  AwesomeFilter.Ginza,
  AwesomeFilter.Hefe,
  // AwesomeFilter.Helena,
  AwesomeFilter.Hudson,
  AwesomeFilter.Inkwell,
  AwesomeFilter.Juno,
  // AwesomeFilter.<PERSON><PERSON>,
  AwesomeFilter.Lark,
  AwesomeFilter.LoFi,
  AwesomeFilter.Ludwig,
  // AwesomeFilter.Maven,
  // AwesomeFilter.Mayfair,
  AwesomeFilter.Moon,
  // AwesomeFilter.Nashville,
  AwesomeFilter.Perpetua,
  AwesomeFilter.Reyes,
  // AwesomeFilter.Rise,
  AwesomeFilter.Sierra,
  // AwesomeFilter.Skyline,
  AwesomeFilter.Slumber,
  AwesomeFilter.Stinson,
  AwesomeFilter.Sutro,
  // AwesomeFilter.Toaster,
  // AwesomeFilter.Valencia,
  // AwesomeFilter.Vesper,
  AwesomeFilter.Walden,
  AwesomeFilter.Willow,
  AwesomeFilter.XProII,
];

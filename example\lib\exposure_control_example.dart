import 'package:camerawesome/camerawesome_plugin.dart';
import 'package:camerawesome/pigeon.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';

void main() {
  runApp(const ExposureControlApp());
}

class ExposureControlApp extends StatelessWidget {
  const ExposureControlApp({super.key});

  @override
  Widget build(BuildContext context) {
    return const MaterialApp(
      title: 'CamerAwesome Exposure Control',
      home: ExposureControlPage(),
    );
  }
}

class ExposureControlPage extends StatelessWidget {
  const ExposureControlPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CameraAwesomeBuilder.awesome(
        saveConfig: SaveConfig.photoAndVideo(
          initialCaptureMode: CaptureMode.photo,
          photoPathBuilder: (sensors) async {
            final Directory extDir = await getTemporaryDirectory();
            final testDir = await Directory(
              '${extDir.path}/camerawesome',
            ).create(recursive: true);
            if (sensors.length == 1) {
              final String filePath =
                  '${testDir.path}/${DateTime.now().millisecondsSinceEpoch}.jpg';
              return SingleCaptureRequest(filePath, sensors.first);
            }
            return MultipleCaptureRequest(
              {
                for (final sensor in sensors)
                  sensor:
                      '${testDir.path}/${sensor.position == SensorPosition.front ? 'front_' : "back_"}${DateTime.now().millisecondsSinceEpoch}.jpg',
              },
            );
          },
          videoOptions: VideoOptions(
            enableAudio: true,
            ios: CupertinoVideoOptions(fps: 30),
            android: AndroidVideoOptions(
              bitrate: 6000000,
              fallbackStrategy: QualityFallbackStrategy.lower,
            ),
          ),
        ),
        sensorConfig: SensorConfig.single(
          sensor: Sensor.position(SensorPosition.back),
          flashMode: FlashMode.auto,
          aspectRatio: CameraAspectRatios.ratio_4_3,
          zoom: 0.0,
        ),
        enablePhysicalButton: true,
        previewAlignment: Alignment.center,
        previewFit: CameraPreviewFit.contain,
        theme: AwesomeTheme(
          bottomActionsBackgroundColor: Colors.black.withOpacity(0.6),
          buttonTheme: AwesomeButtonTheme(
            backgroundColor: Colors.black.withOpacity(0.6),
            iconSize: 24,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.all(14),
            buttonBuilder: (child, onTap) {
              return ClipOval(
                child: Material(
                  color: Colors.transparent,
                  shape: const CircleBorder(),
                  child: InkWell(
                    splashColor: Colors.white.withOpacity(0.3),
                    highlightColor: Colors.white.withOpacity(0.1),
                    onTap: onTap,
                    child: child,
                  ),
                ),
              );
            },
          ),
        ),
        topActionsBuilder: (state) => AwesomeTopActions(
          state: state,
          children: [
            AwesomeFlashButton(state: state),
            const Spacer(),
            AwesomeCameraSwitchButton(
              state: state,
              scale: 1.0,
              onSwitchTap: (state) {
                state.switchCameraSensor(
                  aspectRatio: state.sensorConfig.aspectRatio,
                );
              },
            ),
          ],
        ),
        middleContentBuilder: (state) {
          return Column(
            children: [
              const Spacer(),
              // Exposure control positioned in the middle-right area
              Align(
                alignment: Alignment.centerRight,
                child: Padding(
                  padding: const EdgeInsets.only(right: 20),
                  child: AwesomeExposureSelector(
                    state: state,
                    sliderActiveColor: Colors.white,
                    sliderInactiveColor: Colors.white.withOpacity(0.3),
                    textColor: Colors.white,
                  ),
                ),
              ),
              const Spacer(),
              // Mode indicator
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.6),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Builder(
                  builder: (context) {
                    final mode = state.when(
                      onPhotoMode: (_) => "PHOTO",
                      onVideoMode: (_) => "VIDEO",
                      onVideoRecordingMode: (_) => "RECORDING",
                      onPreparingCamera: (_) => "PREPARING",
                    );

                    return Text(
                      mode,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(height: 20),
            ],
          );
        },
        bottomActionsBuilder: (state) => AwesomeBottomActions(
          state: state,
          captureButton: AwesomeCaptureButton(state: state),
        ),
        onMediaCaptureEvent: (event) {
          switch ((event.status, event.isPicture, event.isVideo)) {
            case (MediaCaptureStatus.capturing, true, false):
              debugPrint('Capturing picture...');
            case (MediaCaptureStatus.success, true, false):
              event.captureRequest.when(
                single: (single) {
                  debugPrint('Picture saved: ${single.file?.path}');
                },
                multiple: (multiple) {
                  multiple.fileBySensor.forEach((key, value) {
                    debugPrint('Multiple image taken: $key ${value?.path}');
                  });
                },
              );
            case (MediaCaptureStatus.failure, true, false):
              debugPrint('Failed to capture picture: ${event.exception}');
            case (MediaCaptureStatus.capturing, false, true):
              debugPrint('Capturing video...');
            case (MediaCaptureStatus.success, false, true):
              event.captureRequest.when(
                single: (single) {
                  debugPrint('Video saved: ${single.file?.path}');
                },
                multiple: (multiple) {
                  multiple.fileBySensor.forEach((key, value) {
                    debugPrint('Multiple video taken: $key ${value?.path}');
                  });
                },
              );
            case (MediaCaptureStatus.failure, false, true):
              debugPrint('Failed to capture video: ${event.exception}');
            default:
              debugPrint('Unknown event: $event');
          }
        },
      ),
    );
  }
}

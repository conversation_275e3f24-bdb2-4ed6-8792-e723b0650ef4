# White Balance Control

The White Balance Control Panel provides professional-grade white balance adjustment for your camera app with both automatic and manual control modes.

## Features

🔘 **Preset Mode Buttons**: Quick access to common white balance scenarios
- ☁️ Cloudy (6500K)
- 🏠 Indoor/Tungsten (3000K) 
- 💡 Fluorescent (4200K)
- ☀️ Daylight (5500K)
- 🌞 Sunny (7000K)
- 🔒 Lock (locks current WB setting)

🎚️ **Kelvin Temperature Slider**: Manual control from 2000K to 9000K with:
- 50K step increments for precise control
- Smooth gradient background (warm orange to cool blue)
- Real-time temperature tooltip display
- Haptic feedback for better UX

🎯 **Auto/Manual Toggle**: Seamless switching between automatic and manual white balance modes

🖌️ **Professional Styling**: Dark UI theme with soft glows, animations, and consistent design

## Basic Usage

### Standard White Balance Selector

```dart
AwesomeWhiteBalanceSelector(
  state: state,
  sliderActiveColor: Colors.white,
  sliderInactiveColor: Colors.white.withOpacity(0.3),
  textColor: Colors.white,
)
```

### Compact Version (No Toggle Button)

```dart
AwesomeCompactWhiteBalanceSelector(
  state: state,
  sliderActiveColor: Colors.cyan,
  sliderInactiveColor: Colors.cyan.withOpacity(0.3),
)
```

## Integration Examples

### Middle Content (Side Panel)

```dart
CameraAwesomeBuilder.awesome(
  middleContentBuilder: (state) {
    return Column(
      children: [
        const Spacer(),
        Align(
          alignment: Alignment.centerRight,
          child: Padding(
            padding: const EdgeInsets.only(right: 20),
            child: AwesomeWhiteBalanceSelector(
              state: state,
              sliderActiveColor: Colors.white,
              sliderInactiveColor: Colors.white.withOpacity(0.3),
              textColor: Colors.white,
            ),
          ),
        ),
        const Spacer(),
      ],
    );
  },
)
```

### Bottom Actions (Compact)

```dart
CameraAwesomeBuilder.awesome(
  bottomActionsBuilder: (state) => Column(
    mainAxisSize: MainAxisSize.min,
    children: [
      AwesomeCompactWhiteBalanceSelector(
        state: state,
        sliderActiveColor: Colors.white,
        sliderInactiveColor: Colors.white.withOpacity(0.3),
      ),
      const SizedBox(height: 8),
      AwesomeBottomActions(
        state: state,
        captureButton: AwesomeCaptureButton(state: state),
      ),
    ],
  ),
)
```

## Customization Options

### Colors and Styling

```dart
AwesomeWhiteBalanceSelector(
  state: state,
  sliderActiveColor: Colors.amber,           // Slider thumb and active track
  sliderInactiveColor: Colors.grey,          // Inactive track color
  textColor: Colors.white,                   // Text and label colors
  padding: EdgeInsets.all(16),               // Panel padding
  showLabel: true,                           // Show temperature label
  showButton: true,                          // Show toggle button
  showResetButton: true,                     // Show reset functionality
)
```

### Visibility Control

```dart
final ValueNotifier<bool> _showWhiteBalance = ValueNotifier(false);

AwesomeWhiteBalanceSelector(
  state: state,
  visibilityNotifier: _showWhiteBalance,     // External visibility control
  showButton: false,                         // Hide built-in toggle button
)
```

## Professional Multi-Control Setup

```dart
Column(
  children: [
    // Exposure control
    AwesomeExposureSelector(
      state: state,
      sliderActiveColor: Colors.amber,
      sliderInactiveColor: Colors.amber.withOpacity(0.3),
      textColor: Colors.white,
    ),
    const SizedBox(height: 16),
    // White balance control
    AwesomeWhiteBalanceSelector(
      state: state,
      sliderActiveColor: Colors.cyan,
      sliderInactiveColor: Colors.cyan.withOpacity(0.3),
      textColor: Colors.white,
    ),
    const SizedBox(height: 16),
    // Shutter speed control
    AwesomeShutterSpeedSelector(
      state: state,
      sliderActiveColor: Colors.green,
      sliderInactiveColor: Colors.green.withOpacity(0.3),
      textColor: Colors.white,
    ),
  ],
)
```

## State Management

The white balance control integrates seamlessly with CamerAwesome's state management:

```dart
// Listen to white balance changes
state.whiteBalance$.listen((kelvin) {
  print('White balance changed to: ${kelvin}K');
});

// Listen to auto mode changes
state.whiteBalanceAuto$.listen((isAuto) {
  print('Auto white balance: $isAuto');
});

// Programmatically set white balance
state.setWhiteBalance(5500.0);  // Set to daylight
state.setWhiteBalanceAuto(true); // Enable auto mode
```

## Technical Details

- **Temperature Range**: 2000K - 9000K
- **Step Size**: 50K increments
- **Auto Mode**: -1.0 value indicates automatic white balance
- **Debounced Updates**: 100ms debounce for smooth performance
- **Haptic Feedback**: Light, medium, and selection click feedback
- **Animation Duration**: 300ms for smooth transitions

## Native Platform Implementation

### Android (CameraX)
The Android implementation uses Camera2 interop to map Kelvin temperatures to predefined white balance modes:
- **2000K-3200K**: Incandescent mode
- **3200K-4000K**: Fluorescent mode
- **4000K-5200K**: Daylight mode
- **5200K-6000K**: Cloudy daylight mode
- **6000K+**: Shade mode
- **Auto Mode**: Continuous auto white balance

### iOS (AVFoundation)
The iOS implementation provides precise Kelvin temperature control using AVFoundation:
- **Direct Kelvin Control**: Uses `deviceWhiteBalanceGainsForTemperatureAndTintValues`
- **Temperature Range**: Full 2000K-9000K range supported
- **Gain Clamping**: Automatically clamps gains to device limits
- **Auto Mode**: Continuous auto white balance mode

## Programmatic Control

```dart
// Set specific temperature
await state.setWhiteBalance(5500.0); // Daylight

// Enable auto mode
await state.setWhiteBalanceAuto(true);

// Listen to changes
state.whiteBalance$.listen((kelvin) {
  print('White balance: ${kelvin == -1.0 ? 'Auto' : '${kelvin}K'}');
});

state.whiteBalanceAuto$.listen((isAuto) {
  print('Auto mode: $isAuto');
});
```

## Examples

See the complete examples in:
- `example/lib/white_balance_control_example.dart`
- `example/lib/main.dart` (integrated example)

### Available Examples:
1. **Basic Integration** - Standard white balance control
2. **Compact Version** - Bottom bar integration
3. **Professional Setup** - Multi-control layout
4. **Programmatic Control** - API testing and automation

The white balance control follows the same architectural patterns as other CamerAwesome selectors, ensuring consistent behavior and styling across your camera app.

## Platform Limitations

- **Android**: CameraX API limitations mean Kelvin temperatures are mapped to predefined modes rather than exact temperature control
- **iOS**: Full Kelvin temperature control available through AVFoundation
- **Web**: White balance control not supported on web platform

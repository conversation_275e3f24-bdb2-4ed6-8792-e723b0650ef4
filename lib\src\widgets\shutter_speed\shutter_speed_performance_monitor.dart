import 'dart:async';
import 'package:flutter/widgets.dart';
import 'package:camerawesome/src/widgets/blur/blur_performance_monitor.dart';

/// Performance monitor specifically for shutter speed effects
/// Extends the existing blur performance monitor with shutter speed specific optimizations
class ShutterSpeedPerformanceMonitor {
  static final ShutterSpeedPerformanceMonitor _instance = ShutterSpeedPerformanceMonitor._internal();
  factory ShutterSpeedPerformanceMonitor() => _instance;
  ShutterSpeedPerformanceMonitor._internal();

  // Performance tracking
  final List<int> _effectProcessingTimes = [];
  double _averageEffectTime = 0.0;
  bool _isInitialized = false;
  
  // Reuse the existing blur performance monitor for FPS tracking
  final BlurPerformanceMonitor _blurMonitor = BlurPerformanceMonitor();

  // Performance thresholds specific to shutter speed effects
  static const double _targetEffectTime = 16.0; // 16ms for 60fps
  static const double _maxEffectTime = 33.0; // 33ms for 30fps
  static const int _effectTimeWindowSize = 20;

  // Device capability detection
  bool get isLowEndDevice => _blurMonitor.isLowEndDevice;
  double get currentFps => _blurMonitor.currentFps;
  double get averageEffectTime => _averageEffectTime;
  bool get shouldUseOptimizedEffects => _averageEffectTime > _targetEffectTime || isLowEndDevice;

  /// Initialize the performance monitor
  Future<void> initialize() async {
    if (_isInitialized) return;

    await _blurMonitor.initialize();
    _isInitialized = true;
  }

  /// Record the time taken to process shutter speed effects
  void recordEffectProcessingTime(int processingTimeMs) {
    if (!_isInitialized) return;

    _effectProcessingTimes.add(processingTimeMs);
    
    // Keep only recent measurements
    while (_effectProcessingTimes.length > _effectTimeWindowSize) {
      _effectProcessingTimes.removeAt(0);
    }

    // Calculate average processing time
    if (_effectProcessingTimes.isNotEmpty) {
      _averageEffectTime = _effectProcessingTimes.reduce((a, b) => a + b) / _effectProcessingTimes.length;
    }
  }

  /// Get optimized brightness adjustment factor based on performance
  double getOptimizedBrightnessAdjustment(double requestedAdjustment) {
    if (!_isInitialized) return requestedAdjustment;

    // If performance is good, use full adjustment
    if (currentFps >= 30.0 && _averageEffectTime <= _targetEffectTime) {
      return requestedAdjustment;
    }

    // If performance is poor, reduce adjustment intensity
    if (currentFps < 20.0 || _averageEffectTime > _maxEffectTime) {
      return requestedAdjustment * 0.7; // Reduce by 30%
    }

    // Moderate performance issues, slight reduction
    return requestedAdjustment * 0.85;
  }

  /// Get optimized motion blur intensity based on performance
  double getOptimizedMotionBlurIntensity(double requestedIntensity) {
    if (!_isInitialized) return requestedIntensity;

    // If performance is good, use requested intensity
    if (currentFps >= 30.0 && _averageEffectTime <= _targetEffectTime) {
      return requestedIntensity;
    }

    // If performance is very poor, disable motion blur
    if (currentFps < 15.0 || _averageEffectTime > _maxEffectTime * 1.5) {
      return 0.0;
    }

    // If performance is poor, significantly reduce intensity
    if (currentFps < 20.0 || _averageEffectTime > _maxEffectTime) {
      return requestedIntensity * 0.4; // Reduce by 60%
    }

    // Moderate performance issues, reduce intensity
    return requestedIntensity * 0.7;
  }

  /// Check if frame blending should be disabled for performance
  bool shouldDisableFrameBlending() {
    return currentFps < 25.0 || _averageEffectTime > _maxEffectTime || isLowEndDevice;
  }

  /// Check if directional blur should be simplified
  bool shouldUseSimplifiedDirectionalBlur() {
    return currentFps < 20.0 || _averageEffectTime > _maxEffectTime;
  }

  /// Get recommended frame blend count based on performance
  int getOptimizedFrameBlendCount(int requestedCount) {
    if (!_isInitialized) return requestedCount;

    if (shouldDisableFrameBlending()) {
      return 0;
    }

    // If performance is good, use requested count
    if (currentFps >= 30.0 && _averageEffectTime <= _targetEffectTime) {
      return requestedCount;
    }

    // If performance is poor, reduce frame count
    if (currentFps < 20.0 || _averageEffectTime > _maxEffectTime) {
      return (requestedCount * 0.5).round().clamp(1, requestedCount);
    }

    // Moderate performance issues, slight reduction
    return (requestedCount * 0.75).round().clamp(1, requestedCount);
  }

  /// Get performance status for debugging
  Map<String, dynamic> getPerformanceStatus() {
    return {
      'currentFps': currentFps,
      'averageEffectTime': _averageEffectTime,
      'isLowEndDevice': isLowEndDevice,
      'shouldUseOptimizedEffects': shouldUseOptimizedEffects,
      'shouldDisableFrameBlending': shouldDisableFrameBlending(),
      'shouldUseSimplifiedDirectionalBlur': shouldUseSimplifiedDirectionalBlur(),
      'effectProcessingTimesCount': _effectProcessingTimes.length,
    };
  }

  /// Dispose resources
  void dispose() {
    _effectProcessingTimes.clear();
  }
}

/// Configuration for shutter speed effect performance
class ShutterSpeedPerformanceConfig {
  /// Enable performance monitoring
  final bool enablePerformanceMonitoring;
  
  /// Maximum allowed effect processing time in milliseconds
  final double maxEffectProcessingTime;
  
  /// Enable adaptive quality based on performance
  final bool enableAdaptiveQuality;
  
  /// FPS threshold below which optimizations are applied
  final double fpsThreshold;
  
  /// Debounce duration for performance adjustments
  final Duration performanceAdjustmentDebounce;

  const ShutterSpeedPerformanceConfig({
    this.enablePerformanceMonitoring = true,
    this.maxEffectProcessingTime = 33.0, // 30fps
    this.enableAdaptiveQuality = true,
    this.fpsThreshold = 25.0,
    this.performanceAdjustmentDebounce = const Duration(milliseconds: 500),
  });

  /// High performance configuration
  static const ShutterSpeedPerformanceConfig highEnd = ShutterSpeedPerformanceConfig(
    enablePerformanceMonitoring: true,
    maxEffectProcessingTime: 16.0, // 60fps
    enableAdaptiveQuality: false, // Disable for high-end devices
    fpsThreshold: 30.0,
  );

  /// Low performance configuration
  static const ShutterSpeedPerformanceConfig lowEnd = ShutterSpeedPerformanceConfig(
    enablePerformanceMonitoring: true,
    maxEffectProcessingTime: 50.0, // 20fps
    enableAdaptiveQuality: true,
    fpsThreshold: 20.0,
    performanceAdjustmentDebounce: Duration(milliseconds: 200),
  );

  /// Auto-detect appropriate configuration
  static ShutterSpeedPerformanceConfig auto() {
    final monitor = ShutterSpeedPerformanceMonitor();
    return monitor.isLowEndDevice ? lowEnd : highEnd;
  }
}

/// Mixin for widgets that need shutter speed performance monitoring
mixin ShutterSpeedPerformanceAware<T extends StatefulWidget> on State<T> {
  final ShutterSpeedPerformanceMonitor _performanceMonitor = ShutterSpeedPerformanceMonitor();
  Timer? _performanceTimer;
  
  @override
  void initState() {
    super.initState();
    _initializePerformanceMonitoring();
  }

  void _initializePerformanceMonitoring() async {
    await _performanceMonitor.initialize();
  }

  /// Measure and record the performance of an effect operation
  Future<R> measureEffectPerformance<R>(Future<R> Function() operation) async {
    final stopwatch = Stopwatch()..start();

    try {
      final result = await operation();
      return result;
    } finally {
      stopwatch.stop();
      _performanceMonitor.recordEffectProcessingTime(stopwatch.elapsedMilliseconds);
    }
  }

  /// Synchronous version of performance measurement
  R measureEffectPerformanceSync<R>(R Function() operation) {
    final stopwatch = Stopwatch()..start();

    try {
      final result = operation();
      return result;
    } finally {
      stopwatch.stop();
      _performanceMonitor.recordEffectProcessingTime(stopwatch.elapsedMilliseconds);
    }
  }

  @override
  void dispose() {
    _performanceTimer?.cancel();
    super.dispose();
  }
}

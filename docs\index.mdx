# CamerAwesome documentation

<Image src="https://github.com/Apparence-io/camera_awesome/blob/master/docs/img/apparence.png?raw=true" alt="Apparence.io flutter studio" />
<Image src="https://github.com/Apparence-io/camera_awesome/blob/master/docs/img/preview.png?raw=true" alt="awesome built-in camera plugin for flutter" />
<Image src="https://github.com/Apparence-io/camera_awesome/blob/master/docs/img/features.png?raw=true" alt="camera flutter plugin features" />

This packages provides you a fully customizable camera experience that you can use within your app. <br/>
Use our awesome built in interface or customize it as you want. 


## Native features
Here's all native features that CamerAwesome provides to the flutter side.

| Features                                 | Android |  iOS  |
| :--------------------------------------- | :-----: | :---: |
| 🔖 Ask permissions                       |    ✅    |   ✅   |
| 🎥 Record video                          |    ✅    |   ✅   |
| 📹 Multi camera                          |    ✅    |   ✅   |
| 🔈 Enable/disable audio                  |    ✅    |   ✅   |
| 🎞 Take photos                           |    ✅    |   ✅   |
| 🌆 Photo live filters                    |    ✅    |   ✅   |
| 🌤 Exposure level                        |    ✅    |   ✅   |
| 📡 Broadcast live image stream           |    ✅    |   ✅   |
| 🧪 Image analysis (barcode scan & more.) |    ✅    |   ✅   |
| 👁 Zoom                                  |    ✅    |   ✅   |
| 📸 Device flash support                  |    ✅    |   ✅   |
| ⌛️ Auto focus                            |    ✅    |   ✅   |
| 📲 Live switching camera                 |    ✅    |   ✅   |
| 😵‍💫 Camera rotation stream                |    ✅    |   ✅   |
| 🤐 Background auto stop                  |    ✅    |   ✅   |
| 🔀 Sensor type switching                 |    ⛔️    |   ✅   |
| 🪞 Enable/disable front camera mirroring |    ✅    |   ✅   |


After [installing](getting_started/installing) CamerAwesome, take a look at the [Awesome built-in UI](getting_started/awesome-ui) guide.
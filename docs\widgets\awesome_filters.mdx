## Filters

CamerAwesome embeds a set of **filters** that can be applied to the camera preview.

There is actually **29** filters which can be applied on your pictures to make them look awesome 🎉.

We are working hard to add more filters to the plugin but you can also [contribute](https://github.com/Apparence-io/CamerAwesome/pulls) by adding your own filters 😎.

![Filters preview](/img/filters.gif)

You can add a filter directly in the camerAwesome builder:

```dart
CameraAwesomeBuilder.awesome(
  // [...]
  filter: AwesomeFilter.AddictiveRed,
),
```

Here is a list of all filters currently available:

|                                                                                                                                                    |                                                                                                                                                                |                                                                                                                                                             |                                                                                                                                                 |
| :------------------------------------------------------------------------------------------------------------------------------------------------: | :------------------------------------------------------------------------------------------------------------------------------------------------------------: | :---------------------------------------------------------------------------------------------------------------------------------------------------------: | :---------------------------------------------------------------------------------------------------------------------------------------------: |
| <img width="1604" alt="No Filter" src="https://raw.githubusercontent.com/skkallayath/photofilters/master/exampleimages/No Filter.jpg" /> No Filter | <img width="1604" alt="AddictiveBlue" src="https://raw.githubusercontent.com/skkallayath/photofilters/master/exampleimages/AddictiveBlue.jpg" /> AddictiveBlue | <img width="1604" alt="AddictiveRed" src="https://raw.githubusercontent.com/skkallayath/photofilters/master/exampleimages/AddictiveRed.jpg" /> AddictiveRed |       <img width="1604" alt="Aden" src="https://raw.githubusercontent.com/skkallayath/photofilters/master/exampleimages/Aden.jpg" /> Aden       |
|       <img width="1604" alt="Amaro" src="https://raw.githubusercontent.com/skkallayath/photofilters/master/exampleimages/Amaro.jpg" /> Amaro       |             <img width="1604" alt="Ashby" src="https://raw.githubusercontent.com/skkallayath/photofilters/master/exampleimages/Ashby.jpg" /> Ashby             |        <img width="1604" alt="Brannan" src="https://raw.githubusercontent.com/skkallayath/photofilters/master/exampleimages/Brannan.jpg" /> Brannan         | <img width="1604" alt="Brooklyn" src="https://raw.githubusercontent.com/skkallayath/photofilters/master/exampleimages/Brooklyn.jpg" /> Brooklyn |
| <img width="1604" alt="Clarendon" src="https://raw.githubusercontent.com/skkallayath/photofilters/master/exampleimages/Clarendon.jpg" /> Clarendon |             <img width="1604" alt="Crema" src="https://raw.githubusercontent.com/skkallayath/photofilters/master/exampleimages/Crema.jpg" /> Crema             |       <img width="1604" alt="Dogpatch" src="https://raw.githubusercontent.com/skkallayath/photofilters/master/exampleimages/Dogpatch.jpg" /> Dogpatch       |     <img width="1604" alt="Sutro" src="https://raw.githubusercontent.com/skkallayath/photofilters/master/exampleimages/Sutro.jpg" /> Sutro      |
|    <img width="1604" alt="Gingham" src="https://raw.githubusercontent.com/skkallayath/photofilters/master/exampleimages/Gingham.jpg" /> Gingham    |             <img width="1604" alt="Ginza" src="https://raw.githubusercontent.com/skkallayath/photofilters/master/exampleimages/Ginza.jpg" /> Ginza             |       <img width="1604" alt="X-Pro II" src="https://raw.githubusercontent.com/skkallayath/photofilters/master/exampleimages/X-Pro II.jpg" /> X-Pro II       |    <img width="1604" alt="Willow" src="https://raw.githubusercontent.com/skkallayath/photofilters/master/exampleimages/Willow.jpg" /> Willow    |
|        <img width="1604" alt="Hefe" src="https://raw.githubusercontent.com/skkallayath/photofilters/master/exampleimages/Hefe.jpg" /> Hefe         |           <img width="1604" alt="Hudson" src="https://raw.githubusercontent.com/skkallayath/photofilters/master/exampleimages/Hudson.jpg" /> Hudson            |        <img width="1604" alt="Inkwell" src="https://raw.githubusercontent.com/skkallayath/photofilters/master/exampleimages/Inkwell.jpg" /> Inkwell         |    <img width="1604" alt="Walden" src="https://raw.githubusercontent.com/skkallayath/photofilters/master/exampleimages/Walden.jpg" /> Walden    |
|        <img width="1604" alt="Juno" src="https://raw.githubusercontent.com/skkallayath/photofilters/master/exampleimages/Juno.jpg" /> Juno         |              <img width="1604" alt="Lark" src="https://raw.githubusercontent.com/skkallayath/photofilters/master/exampleimages/Lark.jpg" /> Lark               |           <img width="1604" alt="Lo-Fi" src="https://raw.githubusercontent.com/skkallayath/photofilters/master/exampleimages/Lo-Fi.jpg" /> Lo-Fi            |  <img width="1604" alt="Stinson" src="https://raw.githubusercontent.com/skkallayath/photofilters/master/exampleimages/Stinson.jpg" /> Stinson   |
|     <img width="1604" alt="Ludwig" src="https://raw.githubusercontent.com/skkallayath/photofilters/master/exampleimages/Ludwig.jpg" /> Ludwig      |              <img width="1604" alt="Moon" src="https://raw.githubusercontent.com/skkallayath/photofilters/master/exampleimages/Moon.jpg" /> Moon               |        <img width="1604" alt="Slumber" src="https://raw.githubusercontent.com/skkallayath/photofilters/master/exampleimages/Slumber.jpg" /> Slumber         |    <img width="1604" alt="Sierra" src="https://raw.githubusercontent.com/skkallayath/photofilters/master/exampleimages/Sierra.jpg" /> Sierra    |
|  <img width="1604" alt="Perpetua" src="https://raw.githubusercontent.com/skkallayath/photofilters/master/exampleimages/Perpetua.jpg" /> Perpetua   |             <img width="1604" alt="Reyes" src="https://raw.githubusercontent.com/skkallayath/photofilters/master/exampleimages/Reyes.jpg" /> Reyes             |

_Preview picture are taken from the [photofilters](https://pub.dev/packages/photofilters) library, thanks to [@skkallayath](https://github.com/skkallayath) for the awesome work!_

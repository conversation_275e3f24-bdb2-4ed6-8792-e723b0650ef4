import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:camerawesome/src/widgets/shutter_speed/shutter_speed_preview_effects.dart';
import 'package:camerawesome/src/widgets/shutter_speed/motion_blur_simulator.dart';

void main() {
  group('Shutter Speed Preview Effects', () {
    testWidgets('AwesomeShutterSpeedPreviewEffect renders without effects in auto mode', (WidgetTester tester) async {
      const testWidget = MaterialApp(
        home: Scaffold(
          body: AwesomeShutterSpeedPreviewEffect(
            shutterSpeedInSeconds: -1.0, // Auto mode
            child: Text('Test Child'),
          ),
        ),
      );

      await tester.pumpWidget(testWidget);
      expect(find.text('Test Child'), findsOneWidget);
    });

    testWidgets('AwesomeShutterSpeedPreviewEffect applies brightness adjustment for fast shutter speeds', (WidgetTester tester) async {
      const testWidget = MaterialApp(
        home: Scaffold(
          body: AwesomeShutterSpeedPreviewEffect(
            shutterSpeedInSeconds: 0.001, // 1/1000s - fast shutter
            child: Text('Test Child'),
          ),
        ),
      );

      await tester.pumpWidget(testWidget);
      await tester.pump(const Duration(milliseconds: 200)); // Wait for debounce
      
      expect(find.text('Test Child'), findsOneWidget);
      expect(find.byType(ColorFiltered), findsOneWidget);
    });

    testWidgets('AwesomeShutterSpeedPreviewEffect applies motion blur for slow shutter speeds', (WidgetTester tester) async {
      const testWidget = MaterialApp(
        home: Scaffold(
          body: AwesomeShutterSpeedPreviewEffect(
            shutterSpeedInSeconds: 0.5, // 1/2s - slow shutter
            child: Text('Test Child'),
          ),
        ),
      );

      await tester.pumpWidget(testWidget);
      await tester.pump(const Duration(milliseconds: 200)); // Wait for debounce
      
      expect(find.text('Test Child'), findsOneWidget);
      expect(find.byType(AwesomePerformanceMotionBlurSimulator), findsOneWidget);
    });

    testWidgets('AwesomePerformanceShutterSpeedEffect uses performance monitoring', (WidgetTester tester) async {
      const testWidget = MaterialApp(
        home: Scaffold(
          body: AwesomePerformanceShutterSpeedEffect(
            shutterSpeedInSeconds: 1.0, // 1s - long exposure
            child: Text('Test Child'),
          ),
        ),
      );

      await tester.pumpWidget(testWidget);
      expect(find.text('Test Child'), findsOneWidget);
      expect(find.byType(AwesomeShutterSpeedPreviewEffect), findsOneWidget);
    });
  });

  group('Motion Blur Simulator', () {
    testWidgets('AwesomeMotionBlurSimulator renders child when disabled', (WidgetTester tester) async {
      const testWidget = MaterialApp(
        home: Scaffold(
          body: AwesomeMotionBlurSimulator(
            blurIntensity: 5.0,
            enabled: false,
            child: Text('Test Child'),
          ),
        ),
      );

      await tester.pumpWidget(testWidget);
      expect(find.text('Test Child'), findsOneWidget);
    });

    testWidgets('AwesomeMotionBlurSimulator applies blur when enabled', (WidgetTester tester) async {
      const testWidget = MaterialApp(
        home: Scaffold(
          body: AwesomeMotionBlurSimulator(
            blurIntensity: 5.0,
            enabled: true,
            child: Text('Test Child'),
          ),
        ),
      );

      await tester.pumpWidget(testWidget);
      expect(find.text('Test Child'), findsOneWidget);
      expect(find.byType(ImageFiltered), findsOneWidget);
    });

    testWidgets('AwesomePerformanceMotionBlurSimulator adapts to performance', (WidgetTester tester) async {
      const testWidget = MaterialApp(
        home: Scaffold(
          body: AwesomePerformanceMotionBlurSimulator(
            blurIntensity: 5.0,
            enabled: true,
            child: Text('Test Child'),
          ),
        ),
      );

      await tester.pumpWidget(testWidget);
      expect(find.text('Test Child'), findsOneWidget);
      expect(find.byType(AwesomeMotionBlurSimulator), findsOneWidget);
    });
  });

  group('Shutter Speed Effect Configuration', () {
    test('ShutterSpeedEffectConfig has correct default values', () {
      const config = ShutterSpeedEffectConfig();
      
      expect(config.enableBrightnessAdjustment, isTrue);
      expect(config.enableMotionBlurSimulation, isTrue);
      expect(config.maxBrightnessAdjustment, equals(2.0));
      expect(config.minBrightnessAdjustment, equals(0.3));
      expect(config.motionBlurThreshold, equals(0.125));
      expect(config.maxMotionBlurIntensity, equals(8.0));
      expect(config.enablePerformanceMonitoring, isTrue);
      expect(config.debounceDuration, equals(const Duration(milliseconds: 100)));
    });

    test('ShutterSpeedEffectConfig high performance preset has correct values', () {
      const config = ShutterSpeedEffectConfig.highPerformance;
      
      expect(config.enableBrightnessAdjustment, isTrue);
      expect(config.enableMotionBlurSimulation, isTrue);
      expect(config.maxBrightnessAdjustment, equals(1.8));
      expect(config.minBrightnessAdjustment, equals(0.4));
      expect(config.maxMotionBlurIntensity, equals(6.0));
      expect(config.debounceDuration, equals(const Duration(milliseconds: 80)));
    });

    test('ShutterSpeedEffectConfig low performance preset disables motion blur', () {
      const config = ShutterSpeedEffectConfig.lowPerformance;
      
      expect(config.enableBrightnessAdjustment, isTrue);
      expect(config.enableMotionBlurSimulation, isFalse);
      expect(config.maxBrightnessAdjustment, equals(1.5));
      expect(config.minBrightnessAdjustment, equals(0.5));
      expect(config.maxMotionBlurIntensity, equals(0.0));
      expect(config.debounceDuration, equals(const Duration(milliseconds: 150)));
    });
  });

  group('Motion Blur Configuration', () {
    test('MotionBlurConfig has correct default values', () {
      const config = MotionBlurConfig();
      
      expect(config.enableFrameBlending, isTrue);
      expect(config.frameBlendCount, equals(3));
      expect(config.frameOpacity, equals(0.3));
      expect(config.enableDirectionalBlur, isTrue);
      expect(config.blurDirection, equals(0.0));
      expect(config.enablePerformanceOptimizations, isTrue);
    });

    test('MotionBlurConfig high performance preset has optimized values', () {
      const config = MotionBlurConfig.highPerformance;
      
      expect(config.enableFrameBlending, isTrue);
      expect(config.frameBlendCount, equals(2));
      expect(config.frameOpacity, equals(0.4));
      expect(config.enableDirectionalBlur, isTrue);
      expect(config.enablePerformanceOptimizations, isTrue);
    });

    test('MotionBlurConfig low performance preset disables frame blending', () {
      const config = MotionBlurConfig.lowPerformance;
      
      expect(config.enableFrameBlending, isFalse);
      expect(config.frameBlendCount, equals(1));
      expect(config.frameOpacity, equals(0.5));
      expect(config.enableDirectionalBlur, isTrue);
      expect(config.enablePerformanceOptimizations, isTrue);
    });
  });
}

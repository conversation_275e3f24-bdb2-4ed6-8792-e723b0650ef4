name: camerawesome
description: Easiest Flutter camera Plugin with builtin UI. Supporting capturing
  images, streaming images, video recording, switch sensors, autofocus, flash,
  filters... on Android and iOS.
version: 2.5.0
homepage: https://Apparence.io
repository: https://github.com/Apparence-io/camera_awesome

environment:
  sdk: '>=3.2.6 <4.0.0'
  flutter: ">=1.10.0"

dependencies:
  carousel_slider: ^5.0.0
  collection: ^1.17.2
  colorfilter_generator: ^0.0.8
  cross_file: ^0.3.3+6
  flutter:
    sdk: flutter
  image: ^4.1.3
  path_provider: ^2.1.1
  rxdart: ^0.28.0
dev_dependencies:
  flutter_lints: ^4.0.0
  flutter_test:
    sdk: flutter
  pigeon: ^21.1.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
# The following section is specific to Flutter.
flutter:
  assets:
    - assets/icons/1_1.png
    - assets/icons/16_9.png
    - assets/icons/4_3.png
    - assets/icons/expanded.png
    - assets/icons/minimized.png
  plugin:
    platforms:
      android:
        package: com.apparence.camerawesome.cameraX
        pluginClass: CameraAwesomeX
      ios:
        pluginClass: CamerawesomePlugin
  #To add assets to your plugin package, add an assets section, like this:
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/assets-and-images/#from-packages
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.
  # To add custom fonts to your plugin package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/custom-fonts/#from-packages

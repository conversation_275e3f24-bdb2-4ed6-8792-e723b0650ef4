## CamerAwesome widgets

CamerAwesome comes with a list of pre-built widgets to ease Camera integration in your app.

Here is a table of all the widgets, with a description and screenshot when appropriate.

| Widget                    | Description                                                                                                                                                                                                                       | Screenshot |
| ------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ---------- | --- |
| CameraAwesomeBuilder      | Main widget with which you should use CamerAwesome. Use either `CameraAwesomeBuilder.awesome()` to use CamerAwesome UI with a few customization or `CameraAwesomeBuilder.awesome()` if you want to entirely build your camera UI. |            |     |
| AwesomeCameraLayout       | Layout used by CameraAwesomeBuilder.                                                                                                                                                                                              |            |     |
| AwesomeCameraActionsRow   |                                                                                                                                                                                                                                   |            |
| AwesomeBottomActions      |                                                                                                                                                                                                                                   |            |
| AwesomeFilterWidget       | Expandable list of filters to use with CamerAwesome in picture mode.                                                                                                                                                              |            |
| AwesomeAspectRatioButton  | Button used to change aspect ratio.<br/>You can customize its behaviour and its look.                                                                                                                                             |            |
| AwesomeCameraSwitchButton | Button used to switch between front and back camera.<br/>You can customize its behaviour and its look.                                                                                                                            |            |
| AwesomeFlashButton        | Button used to switch between flash modes (none, auto, on, always).<br/>You can customize its behaviour and its look.                                                                                                             |            |
| AwesomeLocationButton     | Button used to toggle if location should be save in Exif metadata when taking a picture.<br/>You can customize its behaviour and its look.                                                                                        |            |
| AwesomePauseResumeButton  | Button used to pause or resume a vide recording.<br/>You can customize its behaviour and its look.                                                                                                                                |            |
| AwesomeCameraModeSelector | PageView used to switch between picture mode and video recording mode.<br/>You can customize its behaviour and its look.                                                                                                          |            |
| AwesomeMediaPreview       | Preview of the last media captured.<br/>You can customize its behaviour and its look.                                                                                                                                             |            |
| AwesomeSensorTypeSelector | Selector of sensor types (only iOS).<br/>You can customize its behaviour and its look.                                                                                                                                            |            |
| AwesomeOrientedWidget     | Its child rotates automatically with the camera.<br/>It can be disabled.                                                                                                                                                          |            |
| AwesomeZoomSelector       | Displays the current Zoom and allows to switch to min/max zoom (when min zoom < 1.0)                                                                                                                                              |

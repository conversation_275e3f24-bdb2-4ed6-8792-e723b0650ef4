import 'dart:async';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

/// Configuration for motion blur simulation
class MotionBlurConfig {
  /// Enable frame blending for more realistic motion blur
  final bool enableFrameBlending;
  
  /// Number of frames to blend for motion blur effect
  final int frameBlendCount;
  
  /// Opacity of each blended frame
  final double frameOpacity;
  
  /// Enable directional blur (horizontal motion simulation)
  final bool enableDirectionalBlur;
  
  /// Direction of motion blur (0.0 = horizontal, 0.5 = diagonal, 1.0 = vertical)
  final double blurDirection;
  
  /// Enable performance optimizations
  final bool enablePerformanceOptimizations;

  const MotionBlurConfig({
    this.enableFrameBlending = true,
    this.frameBlendCount = 3,
    this.frameOpacity = 0.3,
    this.enableDirectionalBlur = true,
    this.blurDirection = 0.0, // Horizontal by default
    this.enablePerformanceOptimizations = true,
  });

  /// High performance configuration
  static const MotionBlurConfig highPerformance = MotionBlurConfig(
    enableFrameBlending: true,
    frameBlendCount: 2,
    frameOpacity: 0.4,
    enableDirectionalBlur: true,
    enablePerformanceOptimizations: true,
  );

  /// Low performance configuration
  static const MotionBlurConfig lowPerformance = MotionBlurConfig(
    enableFrameBlending: false,
    frameBlendCount: 1,
    frameOpacity: 0.5,
    enableDirectionalBlur: true,
    enablePerformanceOptimizations: true,
  );
}

/// Widget that simulates motion blur for long exposure photography
class AwesomeMotionBlurSimulator extends StatefulWidget {
  final Widget child;
  final double blurIntensity;
  final MotionBlurConfig config;
  final bool enabled;

  const AwesomeMotionBlurSimulator({
    super.key,
    required this.child,
    required this.blurIntensity,
    this.config = const MotionBlurConfig(),
    this.enabled = true,
  });

  @override
  State<AwesomeMotionBlurSimulator> createState() => _AwesomeMotionBlurSimulatorState();
}

class _AwesomeMotionBlurSimulatorState extends State<AwesomeMotionBlurSimulator>
    with TickerProviderStateMixin {
  final List<ui.Image?> _frameBuffer = [];
  final GlobalKey _repaintBoundaryKey = GlobalKey();
  Timer? _frameCapture;
  late AnimationController _animationController;
  
  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    
    if (widget.config.enableFrameBlending && widget.enabled) {
      _startFrameCapture();
    }
  }

  @override
  void didUpdateWidget(AwesomeMotionBlurSimulator oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.enabled != oldWidget.enabled ||
        widget.config.enableFrameBlending != oldWidget.config.enableFrameBlending) {
      if (widget.config.enableFrameBlending && widget.enabled) {
        _startFrameCapture();
      } else {
        _stopFrameCapture();
      }
    }
  }

  void _startFrameCapture() {
    if (widget.config.enablePerformanceOptimizations) {
      // Capture frames at a lower rate for performance
      _frameCapture = Timer.periodic(const Duration(milliseconds: 33), (_) {
        _captureFrame();
      });
    } else {
      // Capture frames more frequently for better quality
      _frameCapture = Timer.periodic(const Duration(milliseconds: 16), (_) {
        _captureFrame();
      });
    }
  }

  void _stopFrameCapture() {
    _frameCapture?.cancel();
    _frameCapture = null;
    _clearFrameBuffer();
  }

  void _captureFrame() async {
    if (!mounted) return;
    
    try {
      final RenderRepaintBoundary? boundary = _repaintBoundaryKey.currentContext
          ?.findRenderObject() as RenderRepaintBoundary?;

      if (boundary != null) {
        final ui.Image image = await boundary.toImage(pixelRatio: 0.5); // Lower resolution for performance
        
        setState(() {
          _frameBuffer.add(image);
          
          // Keep only the required number of frames
          while (_frameBuffer.length > widget.config.frameBlendCount) {
            final oldImage = _frameBuffer.removeAt(0);
            oldImage?.dispose();
          }
        });
      }
    } catch (e) {
      // Handle capture errors gracefully
      debugPrint('Frame capture error: $e');
    }
  }

  void _clearFrameBuffer() {
    for (final image in _frameBuffer) {
      image?.dispose();
    }
    _frameBuffer.clear();
  }

  @override
  void dispose() {
    _stopFrameCapture();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.enabled || widget.blurIntensity <= 0.0) {
      return widget.child;
    }

    Widget blurredChild = widget.child;

    // Apply directional blur if enabled
    if (widget.config.enableDirectionalBlur) {
      blurredChild = _applyDirectionalBlur(blurredChild);
    } else {
      // Apply standard blur
      blurredChild = _applyStandardBlur(blurredChild);
    }

    // Apply frame blending if enabled and we have frames
    if (widget.config.enableFrameBlending && _frameBuffer.isNotEmpty) {
      blurredChild = _applyFrameBlending(blurredChild);
    }

    return RepaintBoundary(
      key: _repaintBoundaryKey,
      child: blurredChild,
    );
  }

  Widget _applyDirectionalBlur(Widget child) {
    // Calculate blur direction based on configuration
    final double horizontalBlur = widget.blurIntensity * (1.0 - widget.config.blurDirection);
    final double verticalBlur = widget.blurIntensity * widget.config.blurDirection;

    return ImageFiltered(
      imageFilter: ui.ImageFilter.blur(
        sigmaX: horizontalBlur,
        sigmaY: verticalBlur,
        tileMode: TileMode.decal,
      ),
      child: child,
    );
  }

  Widget _applyStandardBlur(Widget child) {
    return ImageFiltered(
      imageFilter: ui.ImageFilter.blur(
        sigmaX: widget.blurIntensity,
        sigmaY: widget.blurIntensity,
        tileMode: TileMode.decal,
      ),
      child: child,
    );
  }

  Widget _applyFrameBlending(Widget child) {
    return Stack(
      children: [
        child,
        ..._frameBuffer.asMap().entries.map((entry) {
          final int index = entry.key;
          final ui.Image? image = entry.value;

          if (image == null) return const SizedBox.shrink();

          // Calculate opacity based on frame age
          final double opacity = widget.config.frameOpacity *
              (1.0 - (index / _frameBuffer.length));

          return Opacity(
            opacity: opacity,
            child: CustomPaint(
              painter: _FrameBlendPainter(image),
              size: Size.infinite,
            ),
          );
        }),
      ],
    );
  }
}

/// Custom painter for blending captured frames
class _FrameBlendPainter extends CustomPainter {
  final ui.Image image;

  _FrameBlendPainter(this.image);

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..blendMode = BlendMode.screen; // Use screen blend mode for light trails effect

    canvas.drawImageRect(
      image,
      Rect.fromLTWH(0, 0, image.width.toDouble(), image.height.toDouble()),
      Rect.fromLTWH(0, 0, size.width, size.height),
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is! _FrameBlendPainter || oldDelegate.image != image;
  }
}

/// Performance-aware motion blur simulator
class AwesomePerformanceMotionBlurSimulator extends StatefulWidget {
  final Widget child;
  final double blurIntensity;
  final MotionBlurConfig config;
  final bool enabled;

  const AwesomePerformanceMotionBlurSimulator({
    super.key,
    required this.child,
    required this.blurIntensity,
    this.config = const MotionBlurConfig(),
    this.enabled = true,
  });

  @override
  State<AwesomePerformanceMotionBlurSimulator> createState() => _AwesomePerformanceMotionBlurSimulatorState();
}

class _AwesomePerformanceMotionBlurSimulatorState extends State<AwesomePerformanceMotionBlurSimulator> {
  bool _isLowPerformanceMode = false;
  
  @override
  void initState() {
    super.initState();
    _detectPerformanceMode();
  }

  void _detectPerformanceMode() {
    // Simple performance detection
    // In a real implementation, you might want to use performance monitoring
    _isLowPerformanceMode = false; // Default to high performance
  }

  @override
  Widget build(BuildContext context) {
    final effectiveConfig = _isLowPerformanceMode 
        ? MotionBlurConfig.lowPerformance
        : widget.config;

    return AwesomeMotionBlurSimulator(
      enabled: widget.enabled,
      blurIntensity: widget.blurIntensity,
      config: effectiveConfig,
      child: widget.child,
    );
  }
}
